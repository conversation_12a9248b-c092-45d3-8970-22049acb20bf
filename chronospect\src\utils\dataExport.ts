/**
 * 数据导出工具函数
 * 支持JSON和CSV格式的数据导出
 */

import type { Activity, TimeBlock, DailyStats } from '@/types';

// 导出数据的版本信息
export const EXPORT_VERSION = '1.0.0';

// 导出数据的完整结构
export interface ExportData {
  version: string;
  exportDate: string;
  metadata: {
    totalDays: number;
    totalActivities: number;
    totalTimeBlocks: number;
    dateRange: {
      start: string;
      end: string;
    };
  };
  data: {
    activities: Activity[];
    timeBlocks: Record<string, TimeBlock[]>;
    dailyStats: Record<string, DailyStats>;
  };
}

// CSV导出的时间块数据结构
export interface CSVTimeBlockData {
  date: string;
  timeSlot: number;
  startTime: string;
  endTime: string;
  activityId: string;
  activityName: string;
  activityColor: string;
  activityDescription: string;
  activityIcon: string;
  duration: number; // 分钟数
  status: string; // 状态：已分配活动/空闲时间
}

/**
 * 导出JSON格式数据
 */
export function exportToJSON(
  activities: Activity[],
  timeBlocks: Record<string, TimeBlock[]>,
  dailyStats: Record<string, DailyStats>,
  dateRange?: { start: string; end: string }
): ExportData {
  // 过滤日期范围内的数据
  let filteredTimeBlocks = timeBlocks;
  let filteredDailyStats = dailyStats;

  if (dateRange) {
    filteredTimeBlocks = {};
    filteredDailyStats = {};

    Object.keys(timeBlocks).forEach(date => {
      if (date >= dateRange.start && date <= dateRange.end) {
        filteredTimeBlocks[date] = timeBlocks[date];
      }
    });

    Object.keys(dailyStats).forEach(date => {
      if (date >= dateRange.start && date <= dateRange.end) {
        filteredDailyStats[date] = dailyStats[date];
      }
    });
  }

  // 计算元数据
  const dates = Object.keys(filteredTimeBlocks);
  const totalTimeBlocks = Object.values(filteredTimeBlocks)
    .reduce((total, blocks) => total + blocks.length, 0);

  const exportData: ExportData = {
    version: EXPORT_VERSION,
    exportDate: new Date().toISOString(),
    metadata: {
      totalDays: dates.length,
      totalActivities: activities.length,
      totalTimeBlocks,
      dateRange: {
        start: dates.length > 0 ? dates.sort()[0] : '',
        end: dates.length > 0 ? dates.sort()[dates.length - 1] : ''
      }
    },
    data: {
      activities,
      timeBlocks: filteredTimeBlocks,
      dailyStats: filteredDailyStats
    }
  };

  return exportData;
}

/**
 * 导出CSV格式数据
 */
export function exportToCSV(
  activities: Activity[],
  timeBlocks: Record<string, TimeBlock[]>,
  dateRange?: { start: string; end: string }
): string {
  // 创建活动映射表
  const activityMap = new Map<string, Activity>();
  activities.forEach(activity => {
    activityMap.set(activity.id, activity);
  });

  // 收集CSV数据
  const csvData: CSVTimeBlockData[] = [];

  Object.entries(timeBlocks).forEach(([date, blocks]) => {
    // 检查日期范围
    if (dateRange && (date < dateRange.start || date > dateRange.end)) {
      return;
    }

    blocks.forEach(block => {
      const activity = block.activityId ? activityMap.get(block.activityId) : null;
      csvData.push({
        date: block.date,
        timeSlot: block.timeSlot,
        startTime: block.startTime,
        endTime: block.endTime,
        activityId: block.activityId || '',
        activityName: activity?.name || '空闲时间',
        activityColor: activity?.color || '#f3f4f6',
        activityDescription: activity?.description || '未分配活动的时间段',
        activityIcon: activity?.icon || '⏰',
        duration: 30, // 每个时间块30分钟
        status: activity ? '已分配活动' : '空闲时间'
      });
    });
  });

  // 按日期和时间槽排序
  csvData.sort((a, b) => {
    if (a.date !== b.date) {
      return a.date.localeCompare(b.date);
    }
    return a.timeSlot - b.timeSlot;
  });

  // 生成CSV内容
  const headers = [
    '日期',
    '时间槽',
    '开始时间',
    '结束时间',
    '活动ID',
    '活动名称',
    '活动颜色',
    '活动描述',
    '活动图标',
    '持续时间(分钟)',
    '状态'
  ];

  const csvRows = [
    headers.join(','),
    ...csvData.map(row => [
      row.date,
      row.timeSlot.toString(),
      row.startTime,
      row.endTime,
      row.activityId,
      `"${row.activityName}"`, // 用引号包围，防止逗号问题
      row.activityColor,
      `"${row.activityDescription}"`, // 用引号包围，防止逗号问题
      row.activityIcon,
      row.duration.toString(),
      `"${row.status}"`
    ].join(','))
  ];

  return csvRows.join('\n');
}

/**
 * 生成导出文件名
 */
export function generateExportFileName(
  format: 'json' | 'csv',
  dateRange?: { start: string; end: string }
): string {
  const now = new Date();
  const timestamp = now.toISOString().split('T')[0]; // YYYY-MM-DD格式

  let fileName = `chronospect_export_${timestamp}`;

  if (dateRange) {
    fileName += `_${dateRange.start}_to_${dateRange.end}`;
  }

  fileName += `.${format}`;
  return fileName;
}

/**
 * 下载文件到本地 - 增强版本，确保在真实浏览器环境中正常工作
 */
export function downloadFile(content: string, fileName: string, mimeType: string): void {
  console.log(`[下载调试] 开始下载文件: ${fileName}, MIME类型: ${mimeType}`);

  try {
    // 确保文件名是安全的，移除可能的特殊字符
    const safeFileName = fileName.replace(/[<>:"/\\|?*]/g, '_');
    console.log(`[下载调试] 安全文件名: ${safeFileName}`);

    // 创建Blob对象，添加BOM以确保UTF-8编码正确
    const bom = mimeType.includes('csv') ? '\uFEFF' : ''; // CSV文件添加BOM
    const blob = new Blob([bom + content], {
      type: mimeType + ';charset=utf-8'
    });
    console.log(`[下载调试] Blob创建成功, 大小: ${blob.size} bytes`);

    // 检查是否支持现代下载API (IE/Edge)
    if (window.navigator && (window.navigator as any).msSaveOrOpenBlob) {
      console.log('[下载调试] 使用IE/Edge下载API');
      (window.navigator as any).msSaveOrOpenBlob(blob, safeFileName);
      return;
    }

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    console.log(`[下载调试] Blob URL创建: ${url}`);

    const link = document.createElement('a');

    // 设置下载属性
    link.href = url;
    link.download = safeFileName;
    link.style.display = 'none';
    link.style.position = 'absolute';
    link.style.left = '-9999px';
    link.style.top = '-9999px';

    // 设置额外属性确保兼容性
    link.setAttribute('download', safeFileName);
    link.setAttribute('rel', 'noopener');

    console.log(`[下载调试] 链接元素创建完成, href: ${link.href}, download: ${link.download}`);

    // 添加到DOM
    document.body.appendChild(link);
    console.log('[下载调试] 链接元素已添加到DOM');

    // 确保DOM更新完成后再触发下载
    requestAnimationFrame(() => {
      try {
        // 触发下载 - 使用多种方法确保兼容性
        console.log('[下载调试] 开始触发下载');

        // 方法1: 直接调用click()
        if (typeof link.click === 'function') {
          console.log('[下载调试] 使用link.click()方法');
          link.click();
        } else {
          // 方法2: 创建并分发点击事件
          console.log('[下载调试] 使用事件分发方法');
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            button: 0
          });
          link.dispatchEvent(clickEvent);
        }

        console.log('[下载调试] 下载触发完成');

        // 延迟清理，确保下载开始
        setTimeout(() => {
          try {
            if (document.body.contains(link)) {
              document.body.removeChild(link);
              console.log('[下载调试] 链接元素已从DOM移除');
            }
            URL.revokeObjectURL(url);
            console.log('[下载调试] Blob URL已释放');
          } catch (cleanupError) {
            console.warn('[下载调试] 清理过程中出现错误:', cleanupError);
          }
        }, 1000); // 增加延迟时间确保下载开始

      } catch (triggerError) {
        console.error('[下载调试] 触发下载时出错:', triggerError);
        // 如果触发失败，尝试备用方法
        fallbackDownload(content, safeFileName, mimeType);
      }
    });

  } catch (error) {
    console.error('[下载调试] 主要下载方法失败:', error);
    fallbackDownload(content, fileName, mimeType);
  }
}

/**
 * 备用下载方法
 */
function fallbackDownload(content: string, fileName: string, mimeType: string): void {
  console.log('[下载调试] 使用备用下载方法');

  try {
    // 方法1: 使用data URL
    const dataUrl = `data:${mimeType};charset=utf-8,${encodeURIComponent(content)}`;
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = fileName;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('[下载调试] 备用方法1 (data URL) 执行完成');
  } catch (dataUrlError) {
    console.error('[下载调试] 备用方法1失败:', dataUrlError);

    try {
      // 方法2: 打开新窗口
      const dataUrl = `data:${mimeType};charset=utf-8,${encodeURIComponent(content)}`;
      const newWindow = window.open(dataUrl, '_blank');

      if (newWindow) {
        console.log('[下载调试] 备用方法2 (新窗口) 执行完成');
      } else {
        console.error('[下载调试] 备用方法2失败: 无法打开新窗口');
        alert('下载失败，请检查浏览器的弹窗拦截设置，或尝试手动保存文件');
      }
    } catch (windowError) {
      console.error('[下载调试] 备用方法2失败:', windowError);
      alert('下载失败，请联系技术支持');
    }
  }
}

/**
 * 导出JSON文件
 */
export function exportJSONFile(
  activities: Activity[],
  timeBlocks: Record<string, TimeBlock[]>,
  dailyStats: Record<string, DailyStats>,
  dateRange?: { start: string; end: string }
): void {
  const exportData = exportToJSON(activities, timeBlocks, dailyStats, dateRange);
  const jsonContent = JSON.stringify(exportData, null, 2);
  const fileName = generateExportFileName('json', dateRange);
  
  downloadFile(jsonContent, fileName, 'application/json');
}

/**
 * 导出CSV文件
 */
export function exportCSVFile(
  activities: Activity[],
  timeBlocks: Record<string, TimeBlock[]>,
  dateRange?: { start: string; end: string }
): void {
  const csvContent = exportToCSV(activities, timeBlocks, dateRange);
  const fileName = generateExportFileName('csv', dateRange);
  
  downloadFile(csvContent, fileName, 'text/csv');
}
