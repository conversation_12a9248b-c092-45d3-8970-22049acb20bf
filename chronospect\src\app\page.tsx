'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';
import { DateNavigator } from '@/components/DateNavigator';
import { TimeGrid } from '@/components/TimeGrid';
import { ReviewView } from '@/components/ReviewView';
import { TimeUpdaterDebugPanel } from '@/components/TimeUpdaterDebugPanel';
import { HydrationBoundary } from '@/components/HydrationBoundary';
import { ActivityManagementModal } from '@/components/ActivityManagementModal';
import { DataManagementDropdown } from '@/components/DataManagementDropdown';
import { DownloadTestPanel } from '@/components/DownloadTestPanel';
import { useViewPreloader } from '@/hooks/useViewPreloader';

export default function Home() {
  const { initializeApp, ui } = useAppStore();
  const [showActivityManagement, setShowActivityManagement] = useState(false);

  // 使用智能预加载Hook
  const {
    isPending,
    switchView,
    handleMouseEnter,
    handleMouseLeave
  } = useViewPreloader();

  // 初始化应用
  useEffect(() => {
    // 确保在客户端环境下初始化，并且在hydration完成后
    if (typeof window !== 'undefined') {
      // 使用requestAnimationFrame确保在hydration完成后初始化
      requestAnimationFrame(() => {
        setTimeout(() => {
          initializeApp();
        }, 50); // 减少延迟，但仍确保hydration完成
      });
    }
  }, [initializeApp]);

  return (
    <div className="min-h-screen" style={{
      background: 'linear-gradient(135deg, var(--primary-50) 0%, white 50%, var(--info-50) 100%)'
    }}>
      {/* 头部 */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-40" style={{
        borderColor: 'var(--neutral-200)',
        boxShadow: 'var(--shadow-sm)'
      }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" style={{ padding: 'var(--spacing-4) var(--spacing-6)' }}>
          <div className="flex items-center justify-between">
            <div>
              <h1 style={{
                fontSize: 'var(--font-size-2xl)',
                fontWeight: 'var(--font-weight-bold)',
                color: 'var(--neutral-900)',
                lineHeight: 'var(--line-height-tight)'
              }}>
                Chronospect
              </h1>
              <p style={{
                fontSize: 'var(--font-size-sm)',
                color: 'var(--neutral-600)',
                marginTop: 'var(--spacing-1)'
              }}>
                时间洞察 - 通过数据理解时间的真实去向
              </p>
            </div>

            {/* 视图切换按钮和数据管理 */}
            <div className="flex items-center" style={{ gap: 'var(--spacing-2)' }}>
              <motion.button
                onClick={() => switchView('grid')}
                onMouseEnter={() => handleMouseEnter('grid')}
                onMouseLeave={handleMouseLeave}
                className={`btn ${ui.currentView === 'grid' ? 'btn-primary' : 'btn-secondary'} ${
                  isPending ? 'opacity-75 cursor-wait' : ''
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.15 }}
                disabled={isPending}
              >
                📊 网格视图
              </motion.button>
              <motion.button
                onClick={() => switchView('review')}
                onMouseEnter={() => handleMouseEnter('review')}
                onMouseLeave={handleMouseLeave}
                className={`btn ${ui.currentView === 'review' ? 'btn-primary' : 'btn-secondary'} ${
                  isPending ? 'opacity-75 cursor-wait' : ''
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.15 }}
                disabled={isPending}
              >
                📈 复盘视图
                {isPending && ui.currentView !== 'review' && (
                  <motion.div
                    className="inline-block ml-2 w-3 h-3 border border-current border-t-transparent rounded-full"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  />
                )}
              </motion.button>

              {/* 数据管理下拉菜单 */}
              <DataManagementDropdown />
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" style={{ padding: 'var(--spacing-8) var(--spacing-6)' }}>
        <HydrationBoundary fallback={
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">加载中...</p>
            </div>
          </div>
        }>
          {/* 日期导航 */}
          <DateNavigator onManageActivities={() => setShowActivityManagement(true)} />

          {/* 根据当前视图显示不同内容 - 优化的无缝切换 */}
          <div className="relative">
            {/* 网格视图 */}
            <motion.div
              className={ui.currentView === 'grid' ? 'block' : 'hidden'}
              initial={false}
              animate={{
                opacity: ui.currentView === 'grid' ? 1 : 0,
                y: ui.currentView === 'grid' ? 0 : 20
              }}
              transition={{
                duration: 0.3,
                ease: [0.4, 0, 0.2, 1]
              }}
            >
              <TimeGrid />
            </motion.div>

            {/* 复盘视图 */}
            <motion.div
              className={ui.currentView === 'review' ? 'block' : 'hidden'}
              initial={false}
              animate={{
                opacity: ui.currentView === 'review' ? 1 : 0,
                y: ui.currentView === 'review' ? 0 : 20
              }}
              transition={{
                duration: 0.3,
                ease: [0.4, 0, 0.2, 1]
              }}
            >
              <ReviewView />
            </motion.div>
          </div>
        </HydrationBoundary>
      </main>

      {/* 活动管理模态框 */}
      <ActivityManagementModal
        isOpen={showActivityManagement}
        onClose={() => setShowActivityManagement(false)}
      />

      {/* 开发环境调试面板 */}
      <TimeUpdaterDebugPanel />

      {/* 下载测试面板 - 仅在开发环境显示 */}
      {process.env.NODE_ENV === 'development' && <DownloadTestPanel />}
    </div>
  );
}
