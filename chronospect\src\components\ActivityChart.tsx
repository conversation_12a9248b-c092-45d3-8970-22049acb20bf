'use client';

import React from 'react';
import { motion } from 'framer-motion';
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Pie, Bar } from 'react-chartjs-2';
import { DailyStats } from '@/types';
import { formatDuration } from '@/utils/timeUtils';

// 注册Chart.js组件
ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ActivityChartProps {
  stats: DailyStats;
}

export function ActivityChart({ stats }: ActivityChartProps) {
  // 如果没有活动数据，显示空状态
  if (stats.activities.length === 0) {
    return (
      <motion.div
        className="card"
        style={{
          background: 'white',
          borderRadius: 'var(--radius-xl)',
          boxShadow: 'var(--shadow-lg)',
          border: '1px solid var(--neutral-200)',
          padding: 'var(--spacing-6)'
        }}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
      >
        <h3 style={{
          fontSize: 'var(--font-size-lg)',
          fontWeight: 'var(--font-weight-semibold)',
          color: 'var(--neutral-900)',
          marginBottom: 'var(--spacing-4)'
        }}>
          📊 活动分布
        </h3>
        <div className="text-center" style={{ padding: 'var(--spacing-12) 0' }}>
          <motion.div
            style={{ fontSize: 'var(--font-size-4xl)', marginBottom: 'var(--spacing-4)' }}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.3, ease: [0.68, -0.55, 0.265, 1.55] }}
          >
            📈
          </motion.div>
          <p style={{ color: 'var(--neutral-500)', fontSize: 'var(--font-size-base)' }}>
            暂无数据可显示
          </p>
        </div>
      </motion.div>
    );
  }

  // 准备饼图数据
  const pieData = {
    labels: stats.activities.map(activity => activity.activityName),
    datasets: [
      {
        data: stats.activities.map(activity => activity.totalMinutes),
        backgroundColor: stats.activities.map(activity => activity.color),
        borderColor: stats.activities.map(activity => activity.color),
        borderWidth: 2,
        hoverBorderWidth: 3,
      },
    ],
  };

  // 准备条形图数据
  const barData = {
    labels: stats.activities.map(activity => activity.activityName),
    datasets: [
      {
        label: '时间 (分钟)',
        data: stats.activities.map(activity => activity.totalMinutes),
        backgroundColor: stats.activities.map(activity => activity.color + '80'), // 添加透明度
        borderColor: stats.activities.map(activity => activity.color),
        borderWidth: 2,
        borderRadius: 6,
        borderSkipped: false,
      },
    ],
  };

  // 图表配置
  const pieOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed;
            const percentage = ((value / stats.filledMinutes) * 100).toFixed(1);
            return `${label}: ${formatDuration(value)} (${percentage}%)`;
          },
        },
      },
    },
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const value = context.parsed.y;
            return `时间: ${formatDuration(value)}`;
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return formatDuration(value);
          },
        },
        grid: {
          color: '#f3f4f6',
        },
      },
      x: {
        grid: {
          display: false,
        },
        ticks: {
          maxRotation: 45,
          minRotation: 0,
        },
      },
    },
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-6)' }}>
      {/* 饼图 */}
      <motion.div
        className="card"
        style={{
          background: 'white',
          borderRadius: 'var(--radius-xl)',
          boxShadow: 'var(--shadow-lg)',
          border: '1px solid var(--neutral-200)',
          padding: 'var(--spacing-6)'
        }}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        whileHover={{
          boxShadow: 'var(--shadow-xl)',
          borderColor: 'var(--neutral-300)'
        }}
      >
        <h3 style={{
          fontSize: 'var(--font-size-lg)',
          fontWeight: 'var(--font-weight-semibold)',
          color: 'var(--neutral-900)',
          marginBottom: 'var(--spacing-4)'
        }}>
          🥧 活动时间分布
        </h3>
        <motion.div
          style={{ height: '320px' }}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
        >
          <Pie data={pieData} options={pieOptions} />
        </motion.div>
      </motion.div>

      {/* 条形图 */}
      <motion.div
        className="card"
        style={{
          background: 'white',
          borderRadius: 'var(--radius-xl)',
          boxShadow: 'var(--shadow-lg)',
          border: '1px solid var(--neutral-200)',
          padding: 'var(--spacing-6)'
        }}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}
        whileHover={{
          boxShadow: 'var(--shadow-xl)',
          borderColor: 'var(--neutral-300)'
        }}
      >
        <h3 style={{
          fontSize: 'var(--font-size-lg)',
          fontWeight: 'var(--font-weight-semibold)',
          color: 'var(--neutral-900)',
          marginBottom: 'var(--spacing-4)'
        }}>
          📊 活动时长对比
        </h3>
        <motion.div
          style={{ height: '320px' }}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
        >
          <Bar data={barData} options={barOptions} />
        </motion.div>
      </motion.div>
    </div>
  );
}
