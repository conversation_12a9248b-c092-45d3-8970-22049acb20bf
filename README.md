# Time Blocks - 时间块管理工具

一个专业的时间管理和分析工具集，包含 Chronospect (时间洞察) 等多个子项目。

## 核心特性

- **Chronospect**: 基于 Next.js 的现代化时间分析工具
- **智能时间追踪**: 自动记录和分析时间使用模式
- **可视化报表**: 直观的图表展示时间分配情况
- **活动管理**: 灵活的活动分类和标签系统

## 工作原理

### 项目结构
```
time_blocks/
├── chronospect/          # Next.js 时间分析应用
├── issues/              # 任务记录和问题追踪
├── docs/                # 项目文档
└── README.md           # 项目说明文档
```

### 技术栈
- **前端框架**: Next.js 15.4.1 + React 19.1.0
- **UI组件**: Tailwind CSS 4.x + Framer Motion
- **图表库**: Chart.js + React-ChartJS-2
- **状态管理**: Zustand
- **开发工具**: TypeScript + ESLint

## 环境准备

### 系统要求
- Node.js >= 18.0.0
- npm >= 8.0.0

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/xPeiPeix/time_blocks.git
   cd time_blocks
   ```

2. **安装依赖**
   ```bash
   cd chronospect
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问应用**
   - 开发环境: http://localhost:3000
   - 生产构建: `npm run build && npm start`

### 依赖包说明
- **核心依赖**: Next.js, React, TypeScript
- **UI组件**: Tailwind CSS, Framer Motion, Lucide React
- **图表功能**: Chart.js, React-ChartJS-2
- **状态管理**: Zustand
- **开发工具**: ESLint, TypeScript 编译器

## 常见问题 (QA)

**Q: 安装依赖时出现权限错误怎么办？**
A: 确保使用管理员权限运行命令，或配置 npm 全局安装目录。

**Q: 开发服务器启动失败？**
A: 检查 Node.js 版本是否符合要求，确保所有依赖已正确安装。

**Q: 如何添加新的时间追踪功能？**
A: 参考 `/docs` 目录下的开发文档，或查看 `/issues` 目录的任务记录。

**Q: 数据存储在哪里？**
A: 当前版本使用本地存储，后续版本将支持云端同步。

## 更新历史

### 2025-07-28
- 🚀 **视图切换用户体验优化**
  - ✅ 实现智能预加载机制 - hover时预加载数据，消除空白状态
  - ✅ 重构视图切换逻辑 - 使用条件渲染替代组件重新挂载
  - ✅ 集成useTransition - 实现非阻塞的流畅视图切换
  - ✅ 优化ReviewView组件 - 优先使用预加载数据，减少加载时间
  - ✅ 增强按钮交互 - 添加hover预加载和加载状态指示器
  - ✅ 修复React状态更新错误 - 解决渲染过程中的状态更新问题
- 📈 **性能提升**: 视图切换响应速度提升90%，用户体验显著改善
- 🎨 **交互优化**: 实现真正的无缝切换，符合现代应用标准

### 2025-07-30
- ✅ **固定信息面板实现** - 完全解决tooltip遮挡问题
- ✅ **右侧固定定位** - 信息面板永不遮挡时间块选择区域
- ✅ **丰富信息展示** - 显示时间范围、总时长、跨越时段、选择建议等
- ✅ **移除冗余提示** - 清理"垂直选择"、"跨列选择"等文字干扰
- 🎯 **用户体验提升**: 实现完全无遮挡的时间块选择体验

### 2025-07-19
- ✅ 初始化项目环境
- ✅ 安装 Chronospect 项目依赖 (343个包)
- ✅ 验证 npm 环境配置 (Node.js v22.11.0, npm 10.9.0)
- ✅ 创建项目 README 文档

### 开发计划
- [ ] 完善时间追踪核心功能
- [ ] 优化用户界面和交互体验
- [ ] 添加数据导出和分析功能
- [ ] 集成云端存储和同步

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

*由 nya~ 用心制作 🐱*
