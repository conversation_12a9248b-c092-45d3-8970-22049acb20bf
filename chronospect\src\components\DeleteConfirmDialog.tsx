'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, Trash2, Info } from 'lucide-react';
import { Activity } from '@/types';
import { useAppStore } from '@/stores/useAppStore';

interface DeleteConfirmDialogProps {
  activity: Activity;
  onConfirm: () => void;
  onCancel: () => void;
}

export function DeleteConfirmDialog({ activity, onConfirm, onCancel }: DeleteConfirmDialogProps) {
  const { timeBlocks } = useAppStore();
  const [isDeleting, setIsDeleting] = useState(false);

  // 检查是否为预设活动
  const presetActivityNames = [
    '深度工作', '会议', '学习', '运动', '休息', '娱乐', '通勤', '用餐'
  ];
  const isPreset = presetActivityNames.includes(activity.name);

  // 计算关联的时间块数量
  const getRelatedTimeBlocksCount = () => {
    let count = 0;
    Object.values(timeBlocks).forEach(dayBlocks => {
      dayBlocks.forEach(block => {
        if (block.activityId === activity.id) {
          count++;
        }
      });
    });
    return count;
  };

  const relatedBlocksCount = getRelatedTimeBlocksCount();

  // 处理确认删除
  const handleConfirm = async () => {
    setIsDeleting(true);
    
    try {
      // 延迟一下以显示删除状态
      await new Promise(resolve => setTimeout(resolve, 500));
      onConfirm();
    } catch (error) {
      console.error('删除活动失败:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="p-8 space-y-6">
      {/* 标题 */}
      <div className="flex items-center space-x-3">
        <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center ${
          isPreset ? 'bg-orange-100' : 'bg-red-100'
        }`}>
          <AlertTriangle className={`w-6 h-6 ${
            isPreset ? 'text-orange-600' : 'text-red-600'
          }`} />
        </div>
        <div>
          <h3 className="text-xl font-semibold text-gray-900">
            {isPreset ? '删除预设活动' : '确认删除活动'}
          </h3>
          <p className="text-sm text-gray-500">
            {isPreset ? '预设活动删除后可能影响系统功能' : '此操作无法撤销'}
          </p>
        </div>
      </div>

      {/* 活动信息 */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className="w-5 h-5 rounded-full border-2 border-white shadow-sm"
              style={{ backgroundColor: activity.color }}
            />
            <div>
              <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                <span>{activity.name}</span>
                {isPreset && (
                  <span className="px-2 py-1 text-xs bg-orange-100 text-orange-700 rounded-full">
                    预设活动
                  </span>
                )}
              </h4>
              {activity.description && (
                <p className="text-sm text-gray-500">{activity.description}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 影响提示 */}
      {relatedBlocksCount > 0 ? (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-medium text-amber-800">影响分析</h4>
              <p className="text-sm text-amber-700 mt-1">
                此活动在 {relatedBlocksCount} 个时间段中被使用。删除后，这些时间段将变为空白。
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-800">影响分析</h4>
              <p className="text-sm text-green-700 mt-1">
                此活动当前未被使用，删除不会影响任何时间记录。
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
        <button
            onClick={onCancel}
            disabled={isDeleting}
            className="px-6 py-3 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            取消
          </button>

          <motion.button
            onClick={handleConfirm}
            disabled={isDeleting}
            className={`flex items-center space-x-2 px-6 py-3 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium ${
              isPreset
                ? 'bg-orange-600 hover:bg-orange-700'
                : 'bg-red-600 hover:bg-red-700'
            }`}
            whileHover={!isDeleting ? { scale: 1.02 } : {}}
            whileTap={!isDeleting ? { scale: 0.98 } : {}}
          >
            {isDeleting ? (
              <>
                <motion.div
                  className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <span>删除中...</span>
              </>
            ) : (
              <>
                <Trash2 className="w-4 h-4" />
                <span>确认删除</span>
              </>
            )}
          </motion.button>
      </div>

      {/* 额外提示 */}
      <div className="text-sm text-gray-600 bg-gray-50 rounded-lg p-4">
        <p className="flex items-start space-x-2">
          <span>💡</span>
          <span>
            {isPreset
              ? '预设活动删除后，您可以重新创建同名活动来恢复功能。'
              : '删除活动后，您可以重新创建同名活动，但历史记录将无法恢复。'
            }
          </span>
        </p>
      </div>
    </div>
  );
}
