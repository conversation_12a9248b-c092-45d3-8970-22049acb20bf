{"file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E5%A4%8D%E7%9B%98%E5%8A%9F%E8%83%BD%E5%BC%80%E5%8F%91_2025-07-18.md": {"language": "<PERSON><PERSON>", "code": 104, "comment": 0, "blank": 27}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/SSR_Hydration_%E4%BF%AE%E5%A4%8D_2025-07-20.md": {"language": "<PERSON><PERSON>", "code": 35, "comment": 0, "blank": 13}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E5%88%97%E4%BC%98%E5%85%88%E5%9E%82%E7%9B%B4%E6%8B%96%E6%8B%BD%E7%AE%97%E6%B3%95%E5%AE%9E%E7%8E%B0_2025-07-21.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 29}, "file:///d%3A/git_resp/Unfinished/time_blocks/docs/%E5%9D%90%E6%A0%87%E7%B3%BB%E7%BB%9F%E8%AF%B4%E6%98%8E.md": {"language": "<PERSON><PERSON>", "code": 99, "comment": 0, "blank": 23}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/Portal%E6%A8%A1%E6%80%81%E6%A1%86%E5%AF%BC%E5%85%A5%E6%B5%81%E7%A8%8B%E4%BC%98%E5%8C%96_2025-07-28.md": {"language": "<PERSON><PERSON>", "code": 84, "comment": 0, "blank": 30}, "file:///d%3A/git_resp/Unfinished/time_blocks/README.md": {"language": "<PERSON><PERSON>", "code": 80, "comment": 0, "blank": 29}, "file:///d%3A/git_resp/Unfinished/time_blocks/docs/%E6%99%BA%E8%83%BD%E6%97%B6%E9%97%B4%E8%A7%A6%E5%8F%91%E5%99%A8%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 48}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/tsconfig.json": {"language": "JSON with Comments", "code": 27, "comment": 0, "blank": 1}, "file:///d%3A/git_resp/Unfinished/time_blocks/%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E6%96%87%E6%A1%A3%20%28PRD%29_%20Chronospect%20%28%E6%97%B6%E9%97%B4%E6%B4%9E%E5%AF%9F%29%20v1.1.md": {"language": "<PERSON><PERSON>", "code": 144, "comment": 0, "blank": 35}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/Chronospect%E7%BE%8E%E5%AD%A6%E4%BC%98%E5%8C%96%E4%B8%8E%E6%B5%8B%E8%AF%95_2025-07-19.md": {"language": "<PERSON><PERSON>", "code": 152, "comment": 0, "blank": 35}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/Chronospect%E5%BC%80%E5%8F%91%E4%BB%BB%E5%8A%A1_2025-07-17.md": {"language": "<PERSON><PERSON>", "code": 133, "comment": 0, "blank": 33}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/types/index.ts": {"language": "TypeScript", "code": 126, "comment": 9, "blank": 11}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/ActivityPalette%E6%94%B9%E9%80%A0_2025-07-18.md": {"language": "<PERSON><PERSON>", "code": 44, "comment": 0, "blank": 9}, "file:///d%3A/git_resp/Unfinished/time_blocks/docs/%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%87%BA%E5%8A%9F%E8%83%BD%E8%AF%B4%E6%98%8E.md": {"language": "<PERSON><PERSON>", "code": 146, "comment": 0, "blank": 41}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/timeUtils.ts": {"language": "TypeScript", "code": 236, "comment": 130, "blank": 63}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/smartTimeUpdater.ts": {"language": "TypeScript", "code": 179, "comment": 82, "blank": 51}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/dragUtils.ts": {"language": "TypeScript", "code": 162, "comment": 106, "blank": 38}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/dataImport.ts": {"language": "TypeScript", "code": 239, "comment": 39, "blank": 44}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/activityImpactAnalysis.ts": {"language": "TypeScript", "code": 134, "comment": 52, "blank": 27}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/loadingUtils.ts": {"language": "TypeScript", "code": 178, "comment": 47, "blank": 33}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/stores/useAppStore.ts": {"language": "TypeScript", "code": 653, "comment": 76, "blank": 111}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/dataExport.ts": {"language": "TypeScript", "code": 276, "comment": 50, "blank": 50}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/cacheUtils.ts": {"language": "TypeScript", "code": 163, "comment": 46, "blank": 33}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/utils/statsUtils.ts": {"language": "TypeScript", "code": 163, "comment": 54, "blank": 30}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/TimeUpdaterDebugPanel.tsx": {"language": "TypeScript JSX", "code": 137, "comment": 12, "blank": 19}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/hooks/useIsClient.ts": {"language": "TypeScript", "code": 19, "comment": 13, "blank": 8}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/TimelineIndicator.tsx": {"language": "TypeScript JSX", "code": 132, "comment": 18, "blank": 21}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/PortalModal.tsx": {"language": "TypeScript JSX", "code": 105, "comment": 9, "blank": 17}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/TimeBlock.tsx": {"language": "TypeScript JSX", "code": 266, "comment": 13, "blank": 18}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/TimeGrid.tsx": {"language": "TypeScript JSX", "code": 382, "comment": 40, "blank": 45}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ImportModal.tsx": {"language": "TypeScript JSX", "code": 244, "comment": 14, "blank": 22}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/HydrationBoundary.tsx": {"language": "TypeScript JSX", "code": 16, "comment": 7, "blank": 7}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DragGuide.tsx": {"language": "TypeScript JSX", "code": 266, "comment": 12, "blank": 21}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DragPreview.tsx": {"language": "TypeScript JSX", "code": 218, "comment": 17, "blank": 22}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DownloadTestPanel.tsx": {"language": "TypeScript JSX", "code": 209, "comment": 4, "blank": 23}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/LoadingSkeleton.tsx": {"language": "TypeScript JSX", "code": 199, "comment": 33, "blank": 19}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DateNavigator.tsx": {"language": "TypeScript JSX", "code": 140, "comment": 8, "blank": 12}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DateComparison.tsx": {"language": "TypeScript JSX", "code": 367, "comment": 17, "blank": 25}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DataManagementDropdown.tsx": {"language": "TypeScript JSX", "code": 164, "comment": 12, "blank": 21}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ReviewView.tsx": {"language": "TypeScript JSX", "code": 228, "comment": 14, "blank": 20}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/IconSelector.tsx": {"language": "TypeScript JSX", "code": 197, "comment": 11, "blank": 20}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/CustomDatePicker.tsx": {"language": "TypeScript JSX", "code": 174, "comment": 12, "blank": 17}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ColorPicker.tsx": {"language": "TypeScript JSX", "code": 171, "comment": 23, "blank": 25}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityPalette.tsx": {"language": "TypeScript JSX", "code": 92, "comment": 13, "blank": 18}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityManagementModal.tsx": {"language": "TypeScript JSX", "code": 317, "comment": 20, "blank": 36}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/CustomDatePicker.module.css": {"language": "PostCSS", "code": 81, "comment": 2, "blank": 17}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityList.tsx": {"language": "TypeScript JSX", "code": 173, "comment": 12, "blank": 17}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DeleteConfirmDialog.tsx": {"language": "TypeScript JSX", "code": 158, "comment": 9, "blank": 15}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/app/page.tsx": {"language": "TypeScript JSX", "code": 112, "comment": 12, "blank": 12}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityChart.tsx": {"language": "TypeScript JSX", "code": 232, "comment": 7, "blank": 11}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/package.json": {"language": "JSON", "code": 33, "comment": 0, "blank": 1}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/postcss.config.mjs": {"language": "JavaScript", "code": 4, "comment": 0, "blank": 2}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/next.config.ts": {"language": "TypeScript", "code": 4, "comment": 1, "blank": 3}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/package-lock.json": {"language": "JSON", "code": 5897, "comment": 0, "blank": 1}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/eslint.config.mjs": {"language": "JavaScript", "code": 12, "comment": 0, "blank": 5}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/app/globals.css": {"language": "PostCSS", "code": 225, "comment": 28, "blank": 44}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/README.md": {"language": "<PERSON><PERSON>", "code": 138, "comment": 0, "blank": 40}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/ActivityForm.tsx": {"language": "TypeScript JSX", "code": 298, "comment": 24, "blank": 39}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E8%B7%A8%E5%88%97%E9%80%89%E6%8B%A9%E5%9D%90%E6%A0%87%E7%B3%BB%E7%BB%9F%E4%BF%AE%E5%A4%8D_2025-07-21.md": {"language": "<PERSON><PERSON>", "code": 56, "comment": 0, "blank": 11}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86%E7%95%8C%E9%9D%A2%E4%BC%98%E5%8C%96_2025-07-22.md": {"language": "<PERSON><PERSON>", "code": 139, "comment": 0, "blank": 40}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/components/DailyStatsCard.tsx": {"language": "TypeScript JSX", "code": 189, "comment": 8, "blank": 12}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E6%97%B6%E9%97%B4%E9%AA%8C%E8%AF%81%E5%8A%9F%E8%83%BD%E4%BF%AE%E5%A4%8D_2025-07-20.md": {"language": "<PERSON><PERSON>", "code": 93, "comment": 0, "blank": 30}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E6%97%B6%E9%97%B4%E8%BD%B4%E6%B5%81%E5%BC%8F%E5%B8%83%E5%B1%80%E6%94%B9%E9%80%A0_2025-07-20.md": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 10}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E6%99%BA%E8%83%BD%E7%B2%BE%E7%A1%AE%E6%97%B6%E9%97%B4%E8%A7%A6%E5%8F%91%E5%99%A8_2025-07-20.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 27}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86CRUD%E5%8A%9F%E8%83%BD%E5%BC%80%E5%8F%91_2025-07-21.md": {"language": "<PERSON><PERSON>", "code": 202, "comment": 0, "blank": 50}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/public/vercel.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/public/globe.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/public/file.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/public/window.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/src/app/layout.tsx": {"language": "TypeScript JSX", "code": 30, "comment": 0, "blank": 5}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%AF%BC%E5%87%BA%E5%8A%9F%E8%83%BD%E5%BC%80%E5%8F%91_2025-07-28.md": {"language": "<PERSON><PERSON>", "code": 143, "comment": 0, "blank": 42}, "file:///d%3A/git_resp/Unfinished/time_blocks/issues/%E6%97%B6%E9%97%B4%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%E7%95%8C%E9%9D%A2%E4%BC%98%E5%8C%96_2025-07-24.md": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 17}, "file:///d%3A/git_resp/Unfinished/time_blocks/chronospect/public/next.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}}