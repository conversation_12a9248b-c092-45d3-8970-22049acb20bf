"filename", "language", "Markdown", "JSON with Comments", "TypeScript", "TypeScript JSX", "PostCSS", "JSON", "JavaScript", "XML", "comment", "blank", "total"
"d:\git_resp\Unfinished\time_blocks\README.md", "Markdown", 80, 0, 0, 0, 0, 0, 0, 0, 0, 29, 109
"d:\git_resp\Unfinished\time_blocks\chronospect\README.md", "Markdown", 138, 0, 0, 0, 0, 0, 0, 0, 0, 40, 178
"d:\git_resp\Unfinished\time_blocks\chronospect\eslint.config.mjs", "JavaScript", 0, 0, 0, 0, 0, 0, 12, 0, 0, 5, 17
"d:\git_resp\Unfinished\time_blocks\chronospect\next.config.ts", "TypeScript", 0, 0, 4, 0, 0, 0, 0, 0, 1, 3, 8
"d:\git_resp\Unfinished\time_blocks\chronospect\package-lock.json", "JSON", 0, 0, 0, 0, 0, 5897, 0, 0, 0, 1, 5898
"d:\git_resp\Unfinished\time_blocks\chronospect\package.json", "JSON", 0, 0, 0, 0, 0, 33, 0, 0, 0, 1, 34
"d:\git_resp\Unfinished\time_blocks\chronospect\postcss.config.mjs", "JavaScript", 0, 0, 0, 0, 0, 0, 4, 0, 0, 2, 6
"d:\git_resp\Unfinished\time_blocks\chronospect\public\file.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1
"d:\git_resp\Unfinished\time_blocks\chronospect\public\globe.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1
"d:\git_resp\Unfinished\time_blocks\chronospect\public\next.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1
"d:\git_resp\Unfinished\time_blocks\chronospect\public\vercel.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1
"d:\git_resp\Unfinished\time_blocks\chronospect\public\window.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1
"d:\git_resp\Unfinished\time_blocks\chronospect\src\app\globals.css", "PostCSS", 0, 0, 0, 0, 225, 0, 0, 0, 28, 44, 297
"d:\git_resp\Unfinished\time_blocks\chronospect\src\app\layout.tsx", "TypeScript JSX", 0, 0, 0, 30, 0, 0, 0, 0, 0, 5, 35
"d:\git_resp\Unfinished\time_blocks\chronospect\src\app\page.tsx", "TypeScript JSX", 0, 0, 0, 112, 0, 0, 0, 0, 12, 12, 136
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityChart.tsx", "TypeScript JSX", 0, 0, 0, 232, 0, 0, 0, 0, 7, 11, 250
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityForm.tsx", "TypeScript JSX", 0, 0, 0, 298, 0, 0, 0, 0, 24, 39, 361
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityList.tsx", "TypeScript JSX", 0, 0, 0, 173, 0, 0, 0, 0, 12, 17, 202
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityManagementModal.tsx", "TypeScript JSX", 0, 0, 0, 317, 0, 0, 0, 0, 20, 36, 373
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityPalette.tsx", "TypeScript JSX", 0, 0, 0, 92, 0, 0, 0, 0, 13, 18, 123
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ColorPicker.tsx", "TypeScript JSX", 0, 0, 0, 171, 0, 0, 0, 0, 23, 25, 219
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\CustomDatePicker.module.css", "PostCSS", 0, 0, 0, 0, 81, 0, 0, 0, 2, 17, 100
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\CustomDatePicker.tsx", "TypeScript JSX", 0, 0, 0, 174, 0, 0, 0, 0, 12, 17, 203
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DailyStatsCard.tsx", "TypeScript JSX", 0, 0, 0, 189, 0, 0, 0, 0, 8, 12, 209
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DataManagementDropdown.tsx", "TypeScript JSX", 0, 0, 0, 164, 0, 0, 0, 0, 12, 21, 197
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DateComparison.tsx", "TypeScript JSX", 0, 0, 0, 367, 0, 0, 0, 0, 17, 25, 409
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DateNavigator.tsx", "TypeScript JSX", 0, 0, 0, 140, 0, 0, 0, 0, 8, 12, 160
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DeleteConfirmDialog.tsx", "TypeScript JSX", 0, 0, 0, 158, 0, 0, 0, 0, 9, 15, 182
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DownloadTestPanel.tsx", "TypeScript JSX", 0, 0, 0, 209, 0, 0, 0, 0, 4, 23, 236
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DragGuide.tsx", "TypeScript JSX", 0, 0, 0, 266, 0, 0, 0, 0, 12, 21, 299
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DragPreview.tsx", "TypeScript JSX", 0, 0, 0, 218, 0, 0, 0, 0, 17, 22, 257
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\HydrationBoundary.tsx", "TypeScript JSX", 0, 0, 0, 16, 0, 0, 0, 0, 7, 7, 30
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\IconSelector.tsx", "TypeScript JSX", 0, 0, 0, 197, 0, 0, 0, 0, 11, 20, 228
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ImportModal.tsx", "TypeScript JSX", 0, 0, 0, 244, 0, 0, 0, 0, 14, 22, 280
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\LoadingSkeleton.tsx", "TypeScript JSX", 0, 0, 0, 199, 0, 0, 0, 0, 33, 19, 251
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\PortalModal.tsx", "TypeScript JSX", 0, 0, 0, 105, 0, 0, 0, 0, 9, 17, 131
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ReviewView.tsx", "TypeScript JSX", 0, 0, 0, 228, 0, 0, 0, 0, 14, 20, 262
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimeBlock.tsx", "TypeScript JSX", 0, 0, 0, 266, 0, 0, 0, 0, 13, 18, 297
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimeGrid.tsx", "TypeScript JSX", 0, 0, 0, 382, 0, 0, 0, 0, 40, 45, 467
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimeUpdaterDebugPanel.tsx", "TypeScript JSX", 0, 0, 0, 137, 0, 0, 0, 0, 12, 19, 168
"d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimelineIndicator.tsx", "TypeScript JSX", 0, 0, 0, 132, 0, 0, 0, 0, 18, 21, 171
"d:\git_resp\Unfinished\time_blocks\chronospect\src\hooks\useIsClient.ts", "TypeScript", 0, 0, 19, 0, 0, 0, 0, 0, 13, 8, 40
"d:\git_resp\Unfinished\time_blocks\chronospect\src\stores\useAppStore.ts", "TypeScript", 0, 0, 653, 0, 0, 0, 0, 0, 76, 111, 840
"d:\git_resp\Unfinished\time_blocks\chronospect\src\types\index.ts", "TypeScript", 0, 0, 126, 0, 0, 0, 0, 0, 9, 11, 146
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\activityImpactAnalysis.ts", "TypeScript", 0, 0, 134, 0, 0, 0, 0, 0, 52, 27, 213
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\cacheUtils.ts", "TypeScript", 0, 0, 163, 0, 0, 0, 0, 0, 46, 33, 242
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\dataExport.ts", "TypeScript", 0, 0, 276, 0, 0, 0, 0, 0, 50, 50, 376
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\dataImport.ts", "TypeScript", 0, 0, 239, 0, 0, 0, 0, 0, 39, 44, 322
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\dragUtils.ts", "TypeScript", 0, 0, 162, 0, 0, 0, 0, 0, 106, 38, 306
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\loadingUtils.ts", "TypeScript", 0, 0, 178, 0, 0, 0, 0, 0, 47, 33, 258
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\smartTimeUpdater.ts", "TypeScript", 0, 0, 179, 0, 0, 0, 0, 0, 82, 51, 312
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\statsUtils.ts", "TypeScript", 0, 0, 163, 0, 0, 0, 0, 0, 54, 30, 247
"d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\timeUtils.ts", "TypeScript", 0, 0, 236, 0, 0, 0, 0, 0, 130, 63, 429
"d:\git_resp\Unfinished\time_blocks\chronospect\tsconfig.json", "JSON with Comments", 0, 27, 0, 0, 0, 0, 0, 0, 0, 1, 28
"d:\git_resp\Unfinished\time_blocks\docs\坐标系统说明.md", "Markdown", 99, 0, 0, 0, 0, 0, 0, 0, 0, 23, 122
"d:\git_resp\Unfinished\time_blocks\docs\数据导出功能说明.md", "Markdown", 146, 0, 0, 0, 0, 0, 0, 0, 0, 41, 187
"d:\git_resp\Unfinished\time_blocks\docs\智能时间触发器使用说明.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 0, 48, 177
"d:\git_resp\Unfinished\time_blocks\issues\ActivityPalette改造_2025-07-18.md", "Markdown", 44, 0, 0, 0, 0, 0, 0, 0, 0, 9, 53
"d:\git_resp\Unfinished\time_blocks\issues\Chronospect开发任务_2025-07-17.md", "Markdown", 133, 0, 0, 0, 0, 0, 0, 0, 0, 33, 166
"d:\git_resp\Unfinished\time_blocks\issues\Chronospect美学优化与测试_2025-07-19.md", "Markdown", 152, 0, 0, 0, 0, 0, 0, 0, 0, 35, 187
"d:\git_resp\Unfinished\time_blocks\issues\Portal模态框导入流程优化_2025-07-28.md", "Markdown", 84, 0, 0, 0, 0, 0, 0, 0, 0, 30, 114
"d:\git_resp\Unfinished\time_blocks\issues\SSR_Hydration_修复_2025-07-20.md", "Markdown", 35, 0, 0, 0, 0, 0, 0, 0, 0, 13, 48
"d:\git_resp\Unfinished\time_blocks\issues\列优先垂直拖拽算法实现_2025-07-21.md", "Markdown", 91, 0, 0, 0, 0, 0, 0, 0, 0, 29, 120
"d:\git_resp\Unfinished\time_blocks\issues\复盘功能开发_2025-07-18.md", "Markdown", 104, 0, 0, 0, 0, 0, 0, 0, 0, 27, 131
"d:\git_resp\Unfinished\time_blocks\issues\数据导入导出功能开发_2025-07-28.md", "Markdown", 143, 0, 0, 0, 0, 0, 0, 0, 0, 42, 185
"d:\git_resp\Unfinished\time_blocks\issues\时间管理系统界面优化_2025-07-24.md", "Markdown", 54, 0, 0, 0, 0, 0, 0, 0, 0, 17, 71
"d:\git_resp\Unfinished\time_blocks\issues\时间轴流式布局改造_2025-07-20.md", "Markdown", 62, 0, 0, 0, 0, 0, 0, 0, 0, 10, 72
"d:\git_resp\Unfinished\time_blocks\issues\时间验证功能修复_2025-07-20.md", "Markdown", 93, 0, 0, 0, 0, 0, 0, 0, 0, 30, 123
"d:\git_resp\Unfinished\time_blocks\issues\智能精确时间触发器_2025-07-20.md", "Markdown", 91, 0, 0, 0, 0, 0, 0, 0, 0, 27, 118
"d:\git_resp\Unfinished\time_blocks\issues\活动管理CRUD功能开发_2025-07-21.md", "Markdown", 202, 0, 0, 0, 0, 0, 0, 0, 0, 50, 252
"d:\git_resp\Unfinished\time_blocks\issues\活动管理界面优化_2025-07-22.md", "Markdown", 139, 0, 0, 0, 0, 0, 0, 0, 0, 40, 179
"d:\git_resp\Unfinished\time_blocks\issues\跨列选择坐标系统修复_2025-07-21.md", "Markdown", 56, 0, 0, 0, 0, 0, 0, 0, 0, 11, 67
"d:\git_resp\Unfinished\time_blocks\产品需求文档 (PRD)_ Chronospect (时间洞察) v1.1.md", "Markdown", 144, 0, 0, 0, 0, 0, 0, 0, 0, 35, 179
"Total", "-", 2219, 27, 2532, 5216, 306, 5930, 16, 5, 1116, 1731, 19098