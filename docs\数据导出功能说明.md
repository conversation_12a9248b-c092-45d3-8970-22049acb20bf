# Chronospect 数据导出功能说明

## 📊 功能概述

Chronospect提供完整的数据导出功能，支持将您的时间记录数据导出为JSON和CSV两种格式，方便进行数据备份、分析或迁移。

## 🚀 如何使用

### 访问导出功能
1. 在Chronospect主界面顶部，点击**"数据管理"**按钮
2. 在下拉菜单中选择：
   - **"导出 JSON"** - 导出完整的结构化数据
   - **"导出 CSV"** - 导出表格格式的时间块数据

### 文件命名规则
导出的文件会自动命名为：
- JSON格式：`chronospect_export_YYYY-MM-DD.json`
- CSV格式：`chronospect_export_YYYY-MM-DD.csv`

其中`YYYY-MM-DD`为导出当天的日期。

## 📋 数据格式说明

### JSON格式导出

JSON格式包含完整的应用数据结构：

```json
{
  "metadata": {
    "exportDate": "2025-07-28T05:44:08.000Z",
    "version": "1.0.0",
    "totalBlocks": 1440,
    "dateRange": {
      "start": "2025-07-28",
      "end": "2025-07-28"
    }
  },
  "activities": [
    {
      "id": "activity-id",
      "name": "活动名称",
      "color": "#3b82f6",
      "icon": "📚",
      "description": "活动描述",
      "isBuiltIn": false
    }
  ],
  "timeBlocks": [
    {
      "id": "block-id",
      "date": "2025-07-28",
      "timeSlot": 0,
      "startTime": "00:00",
      "endTime": "00:30",
      "activityId": "activity-id"
    }
  ]
}
```

#### JSON数据结构说明

**metadata（元数据）**
- `exportDate`: 导出时间戳
- `version`: 数据格式版本
- `totalBlocks`: 总时间块数量
- `dateRange`: 数据的日期范围

**activities（活动定义）**
- `id`: 活动唯一标识符
- `name`: 活动名称
- `color`: 活动颜色（十六进制）
- `icon`: 活动图标（Emoji）
- `description`: 活动描述
- `isBuiltIn`: 是否为内置活动

**timeBlocks（时间块数据）**
- `id`: 时间块唯一标识符
- `date`: 日期（YYYY-MM-DD格式）
- `timeSlot`: 时间槽编号（0-47，代表48个30分钟时间段）
- `startTime`: 开始时间（HH:MM格式）
- `endTime`: 结束时间（HH:MM格式）
- `activityId`: 关联的活动ID（null表示空闲时间）

### CSV格式导出

CSV格式提供表格化的时间块详细信息，包含11个字段：

```csv
日期,时间槽,开始时间,结束时间,活动ID,活动名称,活动颜色,活动描述,活动图标,持续时间(分钟),状态
2025-07-28,0,00:00,00:30,,空闲时间,#f3f4f6,未分配活动的时间段,⏰,30,空闲时间
2025-07-28,8,04:00,04:30,study-id,学习,#3b82f6,学习新知识或技能,📚,30,已分配活动
```

#### CSV字段说明

| 字段名 | 说明 | 示例值 |
|--------|------|--------|
| 日期 | 时间块所属日期 | 2025-07-28 |
| 时间槽 | 时间槽编号（0-47） | 8 |
| 开始时间 | 时间段开始时间 | 04:00 |
| 结束时间 | 时间段结束时间 | 04:30 |
| 活动ID | 活动唯一标识符 | study-id（空闲时间为空） |
| 活动名称 | 活动显示名称 | 学习（空闲时间显示"空闲时间"） |
| 活动颜色 | 活动颜色代码 | #3b82f6（空闲时间为#f3f4f6） |
| 活动描述 | 活动详细描述 | 学习新知识或技能 |
| 活动图标 | 活动图标符号 | 📚（空闲时间为⏰） |
| 持续时间(分钟) | 时间块持续时间 | 30 |
| 状态 | 时间块状态 | 已分配活动/空闲时间 |

## 🔍 数据完整性

### 包含所有时间块
- ✅ **已分配活动的时间块**：包含完整的活动信息
- ✅ **空闲时间块**：标记为"空闲时间"，便于识别未利用的时间

### 空值处理
- **JSON格式**：空闲时间的`activityId`为`null`，这是正常现象
- **CSV格式**：空闲时间的活动ID字段为空，其他字段填充默认值

### 数据一致性
- 两种格式的数据保持完全一致
- 所有时间块都被记录，确保24小时完整覆盖
- 活动信息与时间块数据正确关联

## 📈 使用场景

### 数据备份
- 定期导出数据作为备份
- 防止浏览器数据丢失
- 支持数据迁移到其他设备

### 数据分析
- 使用Excel或其他工具分析CSV数据
- 创建自定义的时间分析报告
- 进行长期的时间使用趋势分析

### 数据集成
- 将JSON数据导入其他时间管理工具
- 与个人数据分析系统集成
- 支持自定义的数据处理流程

## 🛠️ 技术细节

### 文件生成机制
- 使用Blob API在客户端生成文件
- 支持所有现代浏览器
- 无需服务器处理，保护数据隐私

### 性能优化
- 智能数据处理，避免重复计算
- 大数据量时的内存优化
- 流畅的用户体验，导出过程中按钮状态反馈

### 错误处理
- 完善的错误捕获和处理机制
- 用户友好的错误提示
- 自动资源清理，避免内存泄漏

## 🔧 故障排除

### 常见问题

**Q: 导出的文件在哪里？**
A: 文件会下载到浏览器的默认下载文件夹中，通常是`Downloads`文件夹。

**Q: 为什么CSV文件中有很多"空闲时间"？**
A: 这是正常现象。Chronospect记录所有48个时间块，未分配活动的时间块会标记为"空闲时间"。

**Q: JSON文件中的activityId为null是错误吗？**
A: 不是错误。`activityId: null`表示该时间块没有分配活动，即空闲时间。

**Q: 导出功能支持日期范围选择吗？**
A: 当前版本导出所有存储的数据。日期范围选择功能在后续版本中提供。

### 浏览器兼容性
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

---

**更新时间**: 2025-07-28  
**功能状态**: ✅ 已完成并测试验证
