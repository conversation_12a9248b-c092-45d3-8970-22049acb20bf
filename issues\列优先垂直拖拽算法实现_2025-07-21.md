# 列优先垂直拖拽算法实现

**任务日期：** 2025-07-21  
**状态：** 执行中  
**负责人：** nya~

## 任务概述

实现真正的列优先垂直拖拽算法，解决当前智能拖拽系统在垂直拖拽时仍然使用水平选择逻辑的问题。

## 问题分析

### 当前问题
1. **垂直拖拽错误选择**：用户在第一列垂直拖拽（timeSlot 0→12），期望选择 [0,6,12]，但系统选择了 [0,1,2,3,4,5,6,7,8,9,10,11,12]
2. **水平填充逻辑**：系统仍然使用原来的水平选择逻辑，即选择起始到结束之间的所有连续时间块
3. **不符合布局理念**：与6列×8行布局的列优先拖拽理念不符

### 6列×8行时间槽映射
```
列1(深夜): 0,  6, 12, 18, 24, 30, 36, 42
列2(黎明): 1,  7, 13, 19, 25, 31, 37, 43  
列3(上午): 2,  8, 14, 20, 26, 32, 38, 44
列4(下午): 3,  9, 15, 21, 27, 33, 39, 45
列5(傍晚): 4, 10, 16, 22, 28, 34, 40, 46
列6(夜晚): 5, 11, 17, 23, 29, 35, 41, 47
```

## 解决方案

### 1. 完全移除水平拖拽逻辑
- ✅ 删除 `horizontal` 拖拽类型
- ✅ 移除水平拖拽的检测、处理和相关提示信息
- ✅ 简化拖拽方向为：垂直 + 对角

### 2. 实现纯垂直拖拽选择
- ✅ 重写 `getSelectedTimeSlots` 函数
- ✅ 实现 `getVerticalSelection` 函数：只选择同一列内的时间块
- ✅ 确保垂直拖拽不会跨列选择

### 3. 实现列优先的跨列拖拽
- ✅ 实现 `getColumnPrioritySelection` 函数
- ✅ 跨列选择时先填满当前列，再跳转到下一列
- ✅ 保持列优先顺序和时间连续性

### 4. 更新相关组件
- ✅ 修改 `DragPreview` 组件，移除水平拖拽显示
- ✅ 更新 `DragGuide` 用户指南，只保留垂直和跨列拖拽说明
- ✅ 简化拖拽方向检测逻辑

## 技术实现

### 核心算法

#### 垂直拖拽算法
```typescript
function getVerticalSelection(startSlot: number, endSlot: number): number[] {
  const column = startSlot % 6;
  const startRow = Math.floor(startSlot / 6);
  const endRow = Math.floor(endSlot / 6);
  
  const minRow = Math.min(startRow, endRow);
  const maxRow = Math.max(startRow, endRow);
  
  const selectedSlots: number[] = [];
  
  for (let row = minRow; row <= maxRow; row++) {
    const timeSlot = row * 6 + column;
    if (timeSlot < 48) {
      selectedSlots.push(timeSlot);
    }
  }
  
  return selectedSlots;
}
```

#### 列优先跨列选择算法
```typescript
function getColumnPrioritySelection(startSlot: number, endSlot: number): number[] {
  // 按列优先顺序选择
  // 先填满起始列，再跳到下一列继续
  // 确保时间连续性
}
```

## 预期效果

1. **垂直拖拽**：timeSlot 0→12 选择 [0,6,12] 而不是 [0,1,2,3,4,5,6,7,8,9,10,11,12]
2. **跨列拖拽**：按列优先顺序选择，保持时间连续性
3. **用户体验**：拖拽行为更直观，符合6列×8行布局理念
4. **功能兼容**：与现有活动管理功能完全兼容

## 测试计划

- [ ] 测试垂直拖拽选择正确性
- [ ] 测试跨列拖拽的列优先逻辑
- [ ] 验证时间连续性保持
- [ ] 确认与活动管理功能的兼容性

## 测试结果

### 算法验证测试
- ✅ **垂直拖拽测试1**：timeSlot 0→12 正确选择 [0,6,12]（第1列内）
- ✅ **垂直拖拽测试2**：timeSlot 1→13 正确选择 [1,7,13]（第2列内）
- ✅ **跨列拖拽测试1**：timeSlot 0→7 正确选择 [0,1,2,3,4,5,6,7]（连续时间）
- ✅ **跨列拖拽测试2**：timeSlot 6→14 正确选择 [6,7,8,9,10,11,12,13,14]（连续时间）

### 核心改进验证
- ✅ **问题解决**：垂直拖拽不再选择水平填充的时间块
- ✅ **列优先逻辑**：同一列内的垂直拖拽只选择该列的时间槽
- ✅ **时间连续性**：跨列拖拽保持时间的连续性
- ✅ **用户体验**：拖拽行为符合直觉预期

## 进度记录

- ✅ 2025-07-21：开始实施，重写dragUtils.ts核心算法
- ✅ 2025-07-21：更新DragPreview和DragGuide组件
- ✅ 2025-07-21：完成功能测试和验证，所有测试通过
- ✅ 2025-07-21：任务完成，列优先垂直拖拽算法成功实现
