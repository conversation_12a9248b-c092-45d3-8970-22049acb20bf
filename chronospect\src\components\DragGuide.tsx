'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ArrowDown, RotateCcw } from 'lucide-react';

interface DragGuideProps {
  isVisible: boolean;
  onClose: () => void;
}

export function DragGuide({ isVisible, onClose }: DragGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: '列优先拖拽方式',
      description: '在6列时间轴布局中，使用列优先的垂直拖拽来选择连续时间',
      icon: <ArrowDown size={24} />,
      color: 'var(--success-500)',
      demo: 'vertical'
    },
    {
      title: '垂直拖拽',
      description: '在同一列内从上到下拖拽，选择同一时间段内的连续时间（如：深夜 00:00 → 04:00）',
      icon: <ArrowDown size={24} />,
      color: 'var(--success-500)',
      demo: 'vertical-demo'
    },
    {
      title: '跨列选择',
      description: '跨列拖拽时采用列优先算法，先填满当前列再跳到下一列，保持时间连续性',
      icon: <RotateCcw size={24} />,
      color: 'var(--info-500)',
      demo: 'diagonal'
    }
  ];

  const currentStepData = steps[currentStep];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // 自动播放演示动画
  useEffect(() => {
    if (!isVisible) return;

    const timer = setTimeout(() => {
      // 这里可以添加演示动画逻辑
    }, 1000);

    return () => clearTimeout(timer);
  }, [currentStep, isVisible]);

  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* 引导卡片 */}
          <motion.div
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50"
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
          >
            <div
              style={{
                background: 'white',
                borderRadius: 'var(--radius-2xl)',
                boxShadow: 'var(--shadow-2xl)',
                border: '1px solid var(--neutral-200)',
                padding: 'var(--spacing-8)',
                maxWidth: '480px',
                width: '90vw'
              }}
            >
              {/* 关闭按钮 */}
              <button
                onClick={onClose}
                style={{
                  position: 'absolute',
                  top: 'var(--spacing-4)',
                  right: 'var(--spacing-4)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  color: 'var(--neutral-500)',
                  padding: 'var(--spacing-2)',
                  borderRadius: 'var(--radius-md)',
                  transition: 'all var(--duration-fast) var(--ease-out)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--neutral-100)';
                  e.currentTarget.style.color = 'var(--neutral-700)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                  e.currentTarget.style.color = 'var(--neutral-500)';
                }}
              >
                <X size={20} />
              </button>

              {/* 步骤指示器 */}
              <div
                style={{
                  display: 'flex',
                  gap: 'var(--spacing-2)',
                  marginBottom: 'var(--spacing-6)',
                  justifyContent: 'center'
                }}
              >
                {steps.map((_, index) => (
                  <div
                    key={index}
                    style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: index === currentStep ? currentStepData.color : 'var(--neutral-300)',
                      transition: 'all var(--duration-fast) var(--ease-out)'
                    }}
                  />
                ))}
              </div>

              {/* 图标 */}
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  marginBottom: 'var(--spacing-4)'
                }}
              >
                <div
                  style={{
                    width: '64px',
                    height: '64px',
                    borderRadius: '50%',
                    backgroundColor: `${currentStepData.color}20`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: currentStepData.color
                  }}
                >
                  {currentStepData.icon}
                </div>
              </div>

              {/* 标题 */}
              <h3
                style={{
                  fontSize: 'var(--font-size-xl)',
                  fontWeight: 'var(--font-weight-bold)',
                  color: 'var(--neutral-900)',
                  textAlign: 'center',
                  marginBottom: 'var(--spacing-3)'
                }}
              >
                {currentStepData.title}
              </h3>

              {/* 描述 */}
              <p
                style={{
                  fontSize: 'var(--font-size-base)',
                  color: 'var(--neutral-600)',
                  textAlign: 'center',
                  lineHeight: 'var(--line-height-relaxed)',
                  marginBottom: 'var(--spacing-6)'
                }}
              >
                {currentStepData.description}
              </p>

              {/* 演示区域 */}
              <div
                style={{
                  background: 'var(--neutral-50)',
                  borderRadius: 'var(--radius-lg)',
                  padding: 'var(--spacing-4)',
                  marginBottom: 'var(--spacing-6)',
                  minHeight: '120px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <DragDemo type={currentStepData.demo} color={currentStepData.color} />
              </div>

              {/* 按钮组 */}
              <div
                style={{
                  display: 'flex',
                  gap: 'var(--spacing-3)',
                  justifyContent: 'space-between'
                }}
              >
                <button
                  onClick={prevStep}
                  disabled={currentStep === 0}
                  style={{
                    padding: 'var(--spacing-2) var(--spacing-4)',
                    borderRadius: 'var(--radius-md)',
                    border: '1px solid var(--neutral-300)',
                    background: 'white',
                    color: currentStep === 0 ? 'var(--neutral-400)' : 'var(--neutral-700)',
                    cursor: currentStep === 0 ? 'not-allowed' : 'pointer',
                    fontSize: 'var(--font-size-sm)',
                    fontWeight: 'var(--font-weight-medium)',
                    transition: 'all var(--duration-fast) var(--ease-out)'
                  }}
                >
                  上一步
                </button>

                <button
                  onClick={nextStep}
                  style={{
                    padding: 'var(--spacing-2) var(--spacing-6)',
                    borderRadius: 'var(--radius-md)',
                    border: 'none',
                    background: currentStepData.color,
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: 'var(--font-size-sm)',
                    fontWeight: 'var(--font-weight-medium)',
                    transition: 'all var(--duration-fast) var(--ease-out)'
                  }}
                >
                  {currentStep === steps.length - 1 ? '开始使用' : '下一步'}
                </button>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}

// 拖拽演示组件
function DragDemo({ type, color }: { type: string; color: string }) {
  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '4px',
        width: '120px',
        height: '80px'
      }}
    >
      {Array.from({ length: 6 }, (_, i) => (
        <motion.div
          key={i}
          style={{
            background: 'white',
            border: '2px solid var(--neutral-200)',
            borderRadius: '4px',
            position: 'relative'
          }}
          animate={
            type === 'vertical-demo' && (i === 0 || i === 3)
              ? { borderColor: color, backgroundColor: `${color}20` }
              : type === 'diagonal' && (i === 0 || i === 3 || i === 1)
              ? { borderColor: color, backgroundColor: `${color}20` }
              : {}
          }
          transition={{ duration: 0.5, delay: i * 0.1 }}
        />
      ))}
    </div>
  );
}
