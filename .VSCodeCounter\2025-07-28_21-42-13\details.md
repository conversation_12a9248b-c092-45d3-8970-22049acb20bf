# Details

Date : 2025-07-28 21:42:13

Directory d:\\git_resp\\Unfinished\\time_blocks

Total : 73 files,  16251 codes, 1116 comments, 1731 blanks, all 19098 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [README.md](/README.md) | Markdown | 80 | 0 | 29 | 109 |
| [chronospect/README.md](/chronospect/README.md) | Markdown | 138 | 0 | 40 | 178 |
| [chronospect/eslint.config.mjs](/chronospect/eslint.config.mjs) | JavaScript | 12 | 0 | 5 | 17 |
| [chronospect/next.config.ts](/chronospect/next.config.ts) | TypeScript | 4 | 1 | 3 | 8 |
| [chronospect/package-lock.json](/chronospect/package-lock.json) | JSON | 5,897 | 0 | 1 | 5,898 |
| [chronospect/package.json](/chronospect/package.json) | JSON | 33 | 0 | 1 | 34 |
| [chronospect/postcss.config.mjs](/chronospect/postcss.config.mjs) | JavaScript | 4 | 0 | 2 | 6 |
| [chronospect/public/file.svg](/chronospect/public/file.svg) | XML | 1 | 0 | 0 | 1 |
| [chronospect/public/globe.svg](/chronospect/public/globe.svg) | XML | 1 | 0 | 0 | 1 |
| [chronospect/public/next.svg](/chronospect/public/next.svg) | XML | 1 | 0 | 0 | 1 |
| [chronospect/public/vercel.svg](/chronospect/public/vercel.svg) | XML | 1 | 0 | 0 | 1 |
| [chronospect/public/window.svg](/chronospect/public/window.svg) | XML | 1 | 0 | 0 | 1 |
| [chronospect/src/app/globals.css](/chronospect/src/app/globals.css) | PostCSS | 225 | 28 | 44 | 297 |
| [chronospect/src/app/layout.tsx](/chronospect/src/app/layout.tsx) | TypeScript JSX | 30 | 0 | 5 | 35 |
| [chronospect/src/app/page.tsx](/chronospect/src/app/page.tsx) | TypeScript JSX | 112 | 12 | 12 | 136 |
| [chronospect/src/components/ActivityChart.tsx](/chronospect/src/components/ActivityChart.tsx) | TypeScript JSX | 232 | 7 | 11 | 250 |
| [chronospect/src/components/ActivityForm.tsx](/chronospect/src/components/ActivityForm.tsx) | TypeScript JSX | 298 | 24 | 39 | 361 |
| [chronospect/src/components/ActivityList.tsx](/chronospect/src/components/ActivityList.tsx) | TypeScript JSX | 173 | 12 | 17 | 202 |
| [chronospect/src/components/ActivityManagementModal.tsx](/chronospect/src/components/ActivityManagementModal.tsx) | TypeScript JSX | 317 | 20 | 36 | 373 |
| [chronospect/src/components/ActivityPalette.tsx](/chronospect/src/components/ActivityPalette.tsx) | TypeScript JSX | 92 | 13 | 18 | 123 |
| [chronospect/src/components/ColorPicker.tsx](/chronospect/src/components/ColorPicker.tsx) | TypeScript JSX | 171 | 23 | 25 | 219 |
| [chronospect/src/components/CustomDatePicker.module.css](/chronospect/src/components/CustomDatePicker.module.css) | PostCSS | 81 | 2 | 17 | 100 |
| [chronospect/src/components/CustomDatePicker.tsx](/chronospect/src/components/CustomDatePicker.tsx) | TypeScript JSX | 174 | 12 | 17 | 203 |
| [chronospect/src/components/DailyStatsCard.tsx](/chronospect/src/components/DailyStatsCard.tsx) | TypeScript JSX | 189 | 8 | 12 | 209 |
| [chronospect/src/components/DataManagementDropdown.tsx](/chronospect/src/components/DataManagementDropdown.tsx) | TypeScript JSX | 164 | 12 | 21 | 197 |
| [chronospect/src/components/DateComparison.tsx](/chronospect/src/components/DateComparison.tsx) | TypeScript JSX | 367 | 17 | 25 | 409 |
| [chronospect/src/components/DateNavigator.tsx](/chronospect/src/components/DateNavigator.tsx) | TypeScript JSX | 140 | 8 | 12 | 160 |
| [chronospect/src/components/DeleteConfirmDialog.tsx](/chronospect/src/components/DeleteConfirmDialog.tsx) | TypeScript JSX | 158 | 9 | 15 | 182 |
| [chronospect/src/components/DownloadTestPanel.tsx](/chronospect/src/components/DownloadTestPanel.tsx) | TypeScript JSX | 209 | 4 | 23 | 236 |
| [chronospect/src/components/DragGuide.tsx](/chronospect/src/components/DragGuide.tsx) | TypeScript JSX | 266 | 12 | 21 | 299 |
| [chronospect/src/components/DragPreview.tsx](/chronospect/src/components/DragPreview.tsx) | TypeScript JSX | 218 | 17 | 22 | 257 |
| [chronospect/src/components/HydrationBoundary.tsx](/chronospect/src/components/HydrationBoundary.tsx) | TypeScript JSX | 16 | 7 | 7 | 30 |
| [chronospect/src/components/IconSelector.tsx](/chronospect/src/components/IconSelector.tsx) | TypeScript JSX | 197 | 11 | 20 | 228 |
| [chronospect/src/components/ImportModal.tsx](/chronospect/src/components/ImportModal.tsx) | TypeScript JSX | 244 | 14 | 22 | 280 |
| [chronospect/src/components/LoadingSkeleton.tsx](/chronospect/src/components/LoadingSkeleton.tsx) | TypeScript JSX | 199 | 33 | 19 | 251 |
| [chronospect/src/components/PortalModal.tsx](/chronospect/src/components/PortalModal.tsx) | TypeScript JSX | 105 | 9 | 17 | 131 |
| [chronospect/src/components/ReviewView.tsx](/chronospect/src/components/ReviewView.tsx) | TypeScript JSX | 228 | 14 | 20 | 262 |
| [chronospect/src/components/TimeBlock.tsx](/chronospect/src/components/TimeBlock.tsx) | TypeScript JSX | 266 | 13 | 18 | 297 |
| [chronospect/src/components/TimeGrid.tsx](/chronospect/src/components/TimeGrid.tsx) | TypeScript JSX | 382 | 40 | 45 | 467 |
| [chronospect/src/components/TimeUpdaterDebugPanel.tsx](/chronospect/src/components/TimeUpdaterDebugPanel.tsx) | TypeScript JSX | 137 | 12 | 19 | 168 |
| [chronospect/src/components/TimelineIndicator.tsx](/chronospect/src/components/TimelineIndicator.tsx) | TypeScript JSX | 132 | 18 | 21 | 171 |
| [chronospect/src/hooks/useIsClient.ts](/chronospect/src/hooks/useIsClient.ts) | TypeScript | 19 | 13 | 8 | 40 |
| [chronospect/src/stores/useAppStore.ts](/chronospect/src/stores/useAppStore.ts) | TypeScript | 653 | 76 | 111 | 840 |
| [chronospect/src/types/index.ts](/chronospect/src/types/index.ts) | TypeScript | 126 | 9 | 11 | 146 |
| [chronospect/src/utils/activityImpactAnalysis.ts](/chronospect/src/utils/activityImpactAnalysis.ts) | TypeScript | 134 | 52 | 27 | 213 |
| [chronospect/src/utils/cacheUtils.ts](/chronospect/src/utils/cacheUtils.ts) | TypeScript | 163 | 46 | 33 | 242 |
| [chronospect/src/utils/dataExport.ts](/chronospect/src/utils/dataExport.ts) | TypeScript | 276 | 50 | 50 | 376 |
| [chronospect/src/utils/dataImport.ts](/chronospect/src/utils/dataImport.ts) | TypeScript | 239 | 39 | 44 | 322 |
| [chronospect/src/utils/dragUtils.ts](/chronospect/src/utils/dragUtils.ts) | TypeScript | 162 | 106 | 38 | 306 |
| [chronospect/src/utils/loadingUtils.ts](/chronospect/src/utils/loadingUtils.ts) | TypeScript | 178 | 47 | 33 | 258 |
| [chronospect/src/utils/smartTimeUpdater.ts](/chronospect/src/utils/smartTimeUpdater.ts) | TypeScript | 179 | 82 | 51 | 312 |
| [chronospect/src/utils/statsUtils.ts](/chronospect/src/utils/statsUtils.ts) | TypeScript | 163 | 54 | 30 | 247 |
| [chronospect/src/utils/timeUtils.ts](/chronospect/src/utils/timeUtils.ts) | TypeScript | 236 | 130 | 63 | 429 |
| [chronospect/tsconfig.json](/chronospect/tsconfig.json) | JSON with Comments | 27 | 0 | 1 | 28 |
| [docs/坐标系统说明.md](/docs/%E5%9D%90%E6%A0%87%E7%B3%BB%E7%BB%9F%E8%AF%B4%E6%98%8E.md) | Markdown | 99 | 0 | 23 | 122 |
| [docs/数据导出功能说明.md](/docs/%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%87%BA%E5%8A%9F%E8%83%BD%E8%AF%B4%E6%98%8E.md) | Markdown | 146 | 0 | 41 | 187 |
| [docs/智能时间触发器使用说明.md](/docs/%E6%99%BA%E8%83%BD%E6%97%B6%E9%97%B4%E8%A7%A6%E5%8F%91%E5%99%A8%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E.md) | Markdown | 129 | 0 | 48 | 177 |
| [issues/ActivityPalette改造\_2025-07-18.md](/issues/ActivityPalette%E6%94%B9%E9%80%A0_2025-07-18.md) | Markdown | 44 | 0 | 9 | 53 |
| [issues/Chronospect开发任务\_2025-07-17.md](/issues/Chronospect%E5%BC%80%E5%8F%91%E4%BB%BB%E5%8A%A1_2025-07-17.md) | Markdown | 133 | 0 | 33 | 166 |
| [issues/Chronospect美学优化与测试\_2025-07-19.md](/issues/Chronospect%E7%BE%8E%E5%AD%A6%E4%BC%98%E5%8C%96%E4%B8%8E%E6%B5%8B%E8%AF%95_2025-07-19.md) | Markdown | 152 | 0 | 35 | 187 |
| [issues/Portal模态框导入流程优化\_2025-07-28.md](/issues/Portal%E6%A8%A1%E6%80%81%E6%A1%86%E5%AF%BC%E5%85%A5%E6%B5%81%E7%A8%8B%E4%BC%98%E5%8C%96_2025-07-28.md) | Markdown | 84 | 0 | 30 | 114 |
| [issues/SSR\_Hydration\_修复\_2025-07-20.md](/issues/SSR_Hydration_%E4%BF%AE%E5%A4%8D_2025-07-20.md) | Markdown | 35 | 0 | 13 | 48 |
| [issues/列优先垂直拖拽算法实现\_2025-07-21.md](/issues/%E5%88%97%E4%BC%98%E5%85%88%E5%9E%82%E7%9B%B4%E6%8B%96%E6%8B%BD%E7%AE%97%E6%B3%95%E5%AE%9E%E7%8E%B0_2025-07-21.md) | Markdown | 91 | 0 | 29 | 120 |
| [issues/复盘功能开发\_2025-07-18.md](/issues/%E5%A4%8D%E7%9B%98%E5%8A%9F%E8%83%BD%E5%BC%80%E5%8F%91_2025-07-18.md) | Markdown | 104 | 0 | 27 | 131 |
| [issues/数据导入导出功能开发\_2025-07-28.md](/issues/%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5%E5%AF%BC%E5%87%BA%E5%8A%9F%E8%83%BD%E5%BC%80%E5%8F%91_2025-07-28.md) | Markdown | 143 | 0 | 42 | 185 |
| [issues/时间管理系统界面优化\_2025-07-24.md](/issues/%E6%97%B6%E9%97%B4%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%E7%95%8C%E9%9D%A2%E4%BC%98%E5%8C%96_2025-07-24.md) | Markdown | 54 | 0 | 17 | 71 |
| [issues/时间轴流式布局改造\_2025-07-20.md](/issues/%E6%97%B6%E9%97%B4%E8%BD%B4%E6%B5%81%E5%BC%8F%E5%B8%83%E5%B1%80%E6%94%B9%E9%80%A0_2025-07-20.md) | Markdown | 62 | 0 | 10 | 72 |
| [issues/时间验证功能修复\_2025-07-20.md](/issues/%E6%97%B6%E9%97%B4%E9%AA%8C%E8%AF%81%E5%8A%9F%E8%83%BD%E4%BF%AE%E5%A4%8D_2025-07-20.md) | Markdown | 93 | 0 | 30 | 123 |
| [issues/智能精确时间触发器\_2025-07-20.md](/issues/%E6%99%BA%E8%83%BD%E7%B2%BE%E7%A1%AE%E6%97%B6%E9%97%B4%E8%A7%A6%E5%8F%91%E5%99%A8_2025-07-20.md) | Markdown | 91 | 0 | 27 | 118 |
| [issues/活动管理CRUD功能开发\_2025-07-21.md](/issues/%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86CRUD%E5%8A%9F%E8%83%BD%E5%BC%80%E5%8F%91_2025-07-21.md) | Markdown | 202 | 0 | 50 | 252 |
| [issues/活动管理界面优化\_2025-07-22.md](/issues/%E6%B4%BB%E5%8A%A8%E7%AE%A1%E7%90%86%E7%95%8C%E9%9D%A2%E4%BC%98%E5%8C%96_2025-07-22.md) | Markdown | 139 | 0 | 40 | 179 |
| [issues/跨列选择坐标系统修复\_2025-07-21.md](/issues/%E8%B7%A8%E5%88%97%E9%80%89%E6%8B%A9%E5%9D%90%E6%A0%87%E7%B3%BB%E7%BB%9F%E4%BF%AE%E5%A4%8D_2025-07-21.md) | Markdown | 56 | 0 | 11 | 67 |
| [产品需求文档 (PRD)\_ Chronospect (时间洞察) v1.1.md](/%E4%BA%A7%E5%93%81%E9%9C%80%E6%B1%82%E6%96%87%E6%A1%A3%20(PRD)_%20Chronospect%20(%E6%97%B6%E9%97%B4%E6%B4%9E%E5%AF%9F)%20v1.1.md) | Markdown | 144 | 0 | 35 | 179 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)