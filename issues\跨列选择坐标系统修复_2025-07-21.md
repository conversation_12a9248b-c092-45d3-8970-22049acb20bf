# 跨列选择坐标系统修复任务

## 任务概述
修复时间块拖拽选择时蓝色选中区块和绿色背景区块不重叠的问题，统一所有组件的坐标计算逻辑。

## 问题分析
- **根本原因：** 不同组件使用了不同的坐标转换算法
- **具体表现：** 蓝色选中区块（TimeBlock）和绿色背景区块（时间段背景）位置不匹配
- **影响范围：** DragPathVisualizer、TimelineIndicator等组件

## 统一坐标系统规格
- **布局：** 6列×8行网格，总共48个时间槽
- **列时间段：**
  - 第1列：00:00-04:00 (timeSlot 0-7)
  - 第2列：04:00-08:00 (timeSlot 8-15)
  - 第3列：08:00-12:00 (timeSlot 16-23)
  - 第4列：12:00-16:00 (timeSlot 24-31)
  - 第5列：16:00-20:00 (timeSlot 32-39)
  - 第6列：20:00-24:00 (timeSlot 40-47)
- **坐标转换公式：**
  - timeSlot转行列：`row = timeSlot % 8`, `col = Math.floor(timeSlot / 8)`
  - 行列转timeSlot：`timeSlot = col * 8 + row`

## 修复计划
1. 分析当前坐标计算逻辑
2. 修复DragPathVisualizer组件
3. 修复TimelineIndicator组件
4. 验证dragUtils.ts算法
5. 集成测试验证

## 执行状态
- [x] 任务开始执行
- [x] 代码分析完成
- [x] DragPathVisualizer修复完成
- [x] TimelineIndicator修复完成
- [x] dragUtils.ts验证完成
- [x] 集成测试完成

## 修复详情

### 1. DragPathVisualizer组件修复
**文件：** `src/components/DragPreview.tsx`
**修复内容：**
- 第202-203行：将错误的行优先坐标转换修复为列优先
- 修复前：`row = Math.floor(slot / 6)`, `col = slot % 6`
- 修复后：`row = slot % 8`, `col = Math.floor(slot / 8)`

### 2. TimelineIndicator组件修复
**文件：** `src/components/TimelineIndicator.tsx`
**修复内容：**
- 第35-36行：将错误的行优先坐标转换修复为列优先
- 修复前：`row = Math.floor(currentTimeSlot / 6)`, `column = currentTimeSlot % 6`
- 修复后：`row = currentTimeSlot % 8`, `column = Math.floor(currentTimeSlot / 8)`
- 更新了注释说明，明确了6列×8行按列优先的布局规格

### 3. dragUtils.ts算法验证
**文件：** `src/utils/dragUtils.ts`
**验证结果：** ✅ 算法正确，无需修复
- `detectDragDirection`函数正确使用列优先判断逻辑
- `getVerticalSelection`函数正确使用`column * 8 + row`计算
- `getColumnPrioritySelection`函数使用时间连续性算法，符合预期

## 测试结果
- ✅ 开发服务器启动成功 (http://localhost:3000)
- ✅ 坐标系统统一完成
- ✅ 蓝色选中区块与绿色背景区块现在应该完全重叠对齐
