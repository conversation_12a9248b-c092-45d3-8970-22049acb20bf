/* CustomDatePicker 自定义样式 */

.customDayPicker {
  --rdp-accent-color: var(--primary-500);
  --rdp-accent-background-color: var(--primary-50);
  --rdp-background-color: white;
  --rdp-day-height: 44px;
  --rdp-day-width: 44px;
  --rdp-day_button-border-radius: var(--radius-lg);
  --rdp-day_button-border: 1px solid transparent;
  --rdp-selected-border: 1px solid var(--primary-500);
  --rdp-today-color: var(--primary-600);
  --rdp-disabled-opacity: 0.4;
  --rdp-outside-opacity: 0.5;
  --rdp-animation_duration: var(--duration-normal);
  --rdp-animation_timing: var(--ease-out);
}

.rdpRootCustom {
  font-family: var(--font-geist-sans), system-ui, sans-serif;
}

.rdpCaptionCustom {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--neutral-900);
  margin-bottom: var(--spacing-4);
}

.rdpWeekdaysCustom {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--neutral-600);
}

.rdpDayButtonCustom {
  transition: all var(--duration-fast) var(--ease-out);
  font-weight: var(--font-weight-medium);
}

.rdpDayButtonCustom:hover {
  background-color: var(--primary-50) !important;
  color: var(--primary-700) !important;
  transform: scale(1.05);
}

.rdpTodayCustom {
  font-weight: var(--font-weight-bold);
  color: var(--primary-600);
  background-color: var(--primary-50);
}

.rdpSelectedCustom {
  background-color: var(--primary-500) !important;
  color: white !important;
  font-weight: var(--font-weight-bold);
  box-shadow: var(--shadow-md);
}

.rdpChevronCustom {
  fill: var(--primary-500);
  transition: all var(--duration-fast) var(--ease-out);
}

.rdpChevronCustom:hover {
  fill: var(--primary-600);
  transform: scale(1.1);
}

/* 额外的美化样式 */
.customDayPicker :global(.rdp-month_grid) {
  border-spacing: 4px;
}

.customDayPicker :global(.rdp-day) {
  border-radius: var(--radius-lg);
}

.customDayPicker :global(.rdp-outside) {
  opacity: 0.3;
}

.customDayPicker :global(.rdp-nav) {
  margin-bottom: var(--spacing-2);
}

.customDayPicker :global(.rdp-button_previous),
.customDayPicker :global(.rdp-button_next) {
  border-radius: var(--radius-full);
  width: 32px;
  height: 32px;
  transition: all var(--duration-fast) var(--ease-out);
}

.customDayPicker :global(.rdp-button_previous):hover,
.customDayPicker :global(.rdp-button_next):hover {
  background-color: var(--primary-50);
  transform: scale(1.1);
}
