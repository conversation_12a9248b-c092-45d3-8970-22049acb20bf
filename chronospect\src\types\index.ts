// 时间块相关类型定义
export interface TimeBlock {
  id: string;
  date: string; // YYYY-MM-DD格式
  timeSlot: number; // 0-47，代表48个30分钟时间段
  activityId: string | null; // 关联的活动ID
  startTime: string; // HH:MM格式，如 "08:00"
  endTime: string; // HH:MM格式，如 "08:30"
}

// 活动类型定义
export interface Activity {
  id: string;
  name: string;
  color: string; // 十六进制颜色值
  icon?: string; // Lucide图标名称
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 日期相关类型
export interface DateInfo {
  date: string; // YYYY-MM-DD格式
  dayOfWeek: number; // 0-6，0为周日
  isToday: boolean;
  isWeekend: boolean;
}

// 统计数据类型
export interface DailyStats {
  date: string;
  totalMinutes: number; // 一天总分钟数 (24 * 60)
  filledMinutes: number; // 已记录的分钟数
  unfilledMinutes: number; // 未记录的分钟数
  filledPercentage: number; // 已记录时间的百分比
  activities: ActivityStats[];
}

export interface ActivityStats {
  activityId: string;
  activityName: string;
  color: string;
  totalBlocks: number; // 该活动的时间块数量
  totalMinutes: number; // 总分钟数
  percentage: number; // 占已记录时间的百分比
  timeRanges: TimeRange[]; // 时间段列表
}

export interface TimeRange {
  startTime: string;
  endTime: string;
  duration: number; // 分钟数
}

// UI状态类型
export interface UIState {
  selectedBlocks: number[]; // 当前选中的时间块索引
  showActivityPalette: boolean;
  currentView: 'grid' | 'review'; // 当前视图模式
  isDragging: boolean;
  dragStartBlock: number | null;
}

// 应用状态类型
export interface AppState {
  currentDate: string; // 当前查看的日期
  activities: Activity[];
  timeBlocks: Record<string, TimeBlock[]>; // 按日期分组的时间块数据
  ui: UIState;
}

// 颜色预设
export const DEFAULT_COLORS = [
  '#3B82F6', // 蓝色 - 工作
  '#EF4444', // 红色 - 紧急
  '#10B981', // 绿色 - 健康
  '#F59E0B', // 黄色 - 娱乐
  '#8B5CF6', // 紫色 - 学习
  '#EC4899', // 粉色 - 社交
  '#6B7280', // 灰色 - 休息
  '#F97316', // 橙色 - 创意
  '#14B8A6', // 青色 - 运动
  '#84CC16', // 绿黄 - 自然
] as const;

// 默认活动预设
export const DEFAULT_ACTIVITIES: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>[] = [
  {
    name: '深度工作',
    color: DEFAULT_COLORS[0],
    icon: 'Laptop',
    description: '专注的工作时间'
  },
  {
    name: '会议',
    color: DEFAULT_COLORS[1],
    icon: 'Users',
    description: '团队会议或讨论'
  },
  {
    name: '学习',
    color: DEFAULT_COLORS[4],
    icon: 'BookOpen',
    description: '学习新知识或技能'
  },
  {
    name: '运动',
    color: DEFAULT_COLORS[8],
    icon: 'Dumbbell',
    description: '体育锻炼'
  },
  {
    name: '休息',
    color: DEFAULT_COLORS[6],
    icon: 'Coffee',
    description: '休息放松时间'
  },
  {
    name: '娱乐',
    color: DEFAULT_COLORS[3],
    icon: 'Gamepad2',
    description: '娱乐活动'
  },
  {
    name: '通勤',
    color: DEFAULT_COLORS[7],
    icon: 'Car',
    description: '上下班通勤'
  },
  {
    name: '用餐',
    color: DEFAULT_COLORS[9],
    icon: 'Utensils',
    description: '用餐时间'
  }
];

// 时间工具函数类型
export interface TimeUtils {
  formatTime: (timeSlot: number) => { start: string; end: string };
  getTimeSlot: (hour: number, minute: number) => number;
  isValidTimeSlot: (timeSlot: number) => boolean;
  generateTimeBlocks: (date: string) => TimeBlock[];
}
