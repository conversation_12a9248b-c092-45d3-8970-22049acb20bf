'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to detect if code is running on the client side
 * Helps prevent SSR hydration mismatches for client-only features
 * 
 * @returns boolean - true if running on client, false during SSR
 */
export function useIsClient(): boolean {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // This effect only runs on the client side
    setIsClient(true);
  }, []);

  return isClient;
}

/**
 * Hook to safely get current time only on client side
 * Returns null during SSR to prevent hydration mismatches
 * 
 * @returns Date | null - current date on client, null on server
 */
export function useClientTime(): Date | null {
  const isClient = useIsClient();
  const [currentTime, setCurrentTime] = useState<Date | null>(null);

  useEffect(() => {
    if (isClient) {
      setCurrentTime(new Date());
    }
  }, [isClient]);

  return currentTime;
}
