/**
 * LRU缓存实现，用于优化统计数据的缓存管理
 */
export class LRUCache<K, V> {
  private capacity: number;
  private cache: Map<K, V>;

  constructor(capacity: number = 50) {
    this.capacity = capacity;
    this.cache = new Map();
  }

  get(key: K): V | undefined {
    if (this.cache.has(key)) {
      // 移动到最新位置
      const value = this.cache.get(key)!;
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return undefined;
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      // 更新现有值
      this.cache.delete(key);
    } else if (this.cache.size >= this.capacity) {
      // 删除最旧的项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  delete(key: K): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  has(key: K): boolean {
    return this.cache.has(key);
  }

  keys(): IterableIterator<K> {
    return this.cache.keys();
  }
}

/**
 * 数据预加载管理器
 */
export class DataPreloader {
  private static instance: DataPreloader;
  private preloadQueue: Set<string> = new Set();
  private isPreloading = false;

  static getInstance(): DataPreloader {
    if (!DataPreloader.instance) {
      DataPreloader.instance = new DataPreloader();
    }
    return DataPreloader.instance;
  }

  /**
   * 添加日期到预加载队列
   */
  addToQueue(date: string): void {
    this.preloadQueue.add(date);
    this.processQueue();
  }

  /**
   * 批量添加日期到预加载队列
   */
  addMultipleToQueue(dates: string[]): void {
    dates.forEach(date => this.preloadQueue.add(date));
    this.processQueue();
  }

  /**
   * 处理预加载队列
   */
  private async processQueue(): Promise<void> {
    if (this.isPreloading || this.preloadQueue.size === 0) {
      return;
    }

    this.isPreloading = true;

    try {
      // 使用requestIdleCallback在浏览器空闲时预加载
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          this.performPreload();
        });
      } else {
        // 降级到setTimeout
        setTimeout(() => {
          this.performPreload();
        }, 100);
      }
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * 执行预加载
   */
  private performPreload(): void {
    const dates = Array.from(this.preloadQueue);
    this.preloadQueue.clear();

    // 这里会在store中实现具体的预加载逻辑
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('preloadDates', { 
        detail: { dates } 
      }));
    }
  }

  /**
   * 清空预加载队列
   */
  clearQueue(): void {
    this.preloadQueue.clear();
  }
}

/**
 * 获取相邻日期列表，用于预加载
 */
export function getAdjacentDates(currentDate: string, range: number = 3): string[] {
  const dates: string[] = [];
  const baseDate = new Date(currentDate);

  for (let i = -range; i <= range; i++) {
    if (i === 0) continue; // 跳过当前日期
    const date = new Date(baseDate);
    date.setDate(baseDate.getDate() + i);
    dates.push(date.toISOString().split('T')[0]);
  }

  return dates;
}

/**
 * 内存使用监控器
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private memoryThreshold = 50 * 1024 * 1024; // 50MB

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  /**
   * 检查内存使用情况
   */
  checkMemoryUsage(): boolean {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return false;
    }

    try {
      // @ts-ignore - performance.memory可能不存在
      const memory = window.performance.memory;
      if (memory && memory.usedJSHeapSize > this.memoryThreshold) {
        return true; // 内存使用过高
      }
    } catch (error) {
      console.warn('无法检查内存使用情况:', error);
    }

    return false;
  }

  /**
   * 设置内存阈值
   */
  setThreshold(threshold: number): void {
    this.memoryThreshold = threshold;
  }
}

/**
 * 缓存性能统计
 */
export interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  size: number;
  capacity: number;
}

/**
 * 带统计功能的LRU缓存
 */
export class StatisticalLRUCache<K, V> extends LRUCache<K, V> {
  private hits = 0;
  private misses = 0;

  get(key: K): V | undefined {
    const value = super.get(key);
    if (value !== undefined) {
      this.hits++;
    } else {
      this.misses++;
    }
    return value;
  }

  getStats(): CacheStats {
    const total = this.hits + this.misses;
    return {
      hits: this.hits,
      misses: this.misses,
      hitRate: total > 0 ? this.hits / total : 0,
      size: this.size(),
      capacity: this.capacity
    };
  }

  resetStats(): void {
    this.hits = 0;
    this.misses = 0;
  }
}
