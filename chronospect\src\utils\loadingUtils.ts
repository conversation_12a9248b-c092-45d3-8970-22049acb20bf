/**
 * 加载状态管理工具
 */

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface LoadingStatus {
  state: LoadingState;
  message?: string;
  progress?: number;
  startTime?: number;
}

/**
 * 加载状态管理器
 */
export class LoadingManager {
  private static instance: LoadingManager;
  private loadingStates: Map<string, LoadingStatus> = new Map();
  private listeners: Map<string, Set<(status: LoadingStatus) => void>> = new Map();

  static getInstance(): LoadingManager {
    if (!LoadingManager.instance) {
      LoadingManager.instance = new LoadingManager();
    }
    return LoadingManager.instance;
  }

  /**
   * 设置加载状态
   */
  setLoadingState(key: string, state: LoadingState, message?: string, progress?: number): void {
    const currentStatus = this.loadingStates.get(key);
    const newStatus: LoadingStatus = {
      state,
      message,
      progress,
      startTime: state === 'loading' ? Date.now() : currentStatus?.startTime
    };

    this.loadingStates.set(key, newStatus);
    this.notifyListeners(key, newStatus);
  }

  /**
   * 获取加载状态
   */
  getLoadingState(key: string): LoadingStatus {
    return this.loadingStates.get(key) || { state: 'idle' };
  }

  /**
   * 监听加载状态变化
   */
  subscribe(key: string, callback: (status: LoadingStatus) => void): () => void {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key)!.add(callback);

    // 返回取消订阅函数
    return () => {
      const listeners = this.listeners.get(key);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.listeners.delete(key);
        }
      }
    };
  }

  /**
   * 通知监听器
   */
  private notifyListeners(key: string, status: LoadingStatus): void {
    const listeners = this.listeners.get(key);
    if (listeners) {
      listeners.forEach(callback => callback(status));
    }
  }

  /**
   * 清除加载状态
   */
  clearLoadingState(key: string): void {
    this.loadingStates.delete(key);
    this.notifyListeners(key, { state: 'idle' });
  }

  /**
   * 清除所有加载状态
   */
  clearAllLoadingStates(): void {
    const keys = Array.from(this.loadingStates.keys());
    this.loadingStates.clear();
    keys.forEach(key => this.notifyListeners(key, { state: 'idle' }));
  }

  /**
   * 获取加载持续时间
   */
  getLoadingDuration(key: string): number {
    const status = this.loadingStates.get(key);
    if (status?.startTime && status.state === 'loading') {
      return Date.now() - status.startTime;
    }
    return 0;
  }
}

/**
 * 防抖加载函数
 */
export function debounceLoading<T extends any[]>(
  fn: (...args: T) => Promise<any>,
  delay: number = 300
): (...args: T) => Promise<any> {
  let timeoutId: NodeJS.Timeout;
  let lastPromise: Promise<any> | null = null;

  return (...args: T): Promise<any> => {
    return new Promise((resolve, reject) => {
      clearTimeout(timeoutId);
      
      timeoutId = setTimeout(async () => {
        try {
          const result = await fn(...args);
          lastPromise = null;
          resolve(result);
        } catch (error) {
          lastPromise = null;
          reject(error);
        }
      }, delay);
    });
  };
}

/**
 * 批量加载管理器
 */
export class BatchLoader<T, R> {
  private batchSize: number;
  private batchTimeout: number;
  private pendingItems: T[] = [];
  private pendingPromises: Array<{
    resolve: (value: R[]) => void;
    reject: (error: any) => void;
  }> = [];
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(
    private loadFn: (items: T[]) => Promise<R[]>,
    batchSize: number = 10,
    batchTimeout: number = 100
  ) {
    this.batchSize = batchSize;
    this.batchTimeout = batchTimeout;
  }

  /**
   * 添加项目到批量加载队列
   */
  load(item: T): Promise<R> {
    return new Promise<R>((resolve, reject) => {
      this.pendingItems.push(item);
      this.pendingPromises.push({
        resolve: (results: R[]) => {
          const index = this.pendingItems.length - 1;
          resolve(results[index]);
        },
        reject
      });

      this.scheduleFlush();
    });
  }

  /**
   * 调度批量处理
   */
  private scheduleFlush(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    if (this.pendingItems.length >= this.batchSize) {
      this.flush();
    } else {
      this.timeoutId = setTimeout(() => {
        this.flush();
      }, this.batchTimeout);
    }
  }

  /**
   * 执行批量加载
   */
  private async flush(): Promise<void> {
    if (this.pendingItems.length === 0) {
      return;
    }

    const items = [...this.pendingItems];
    const promises = [...this.pendingPromises];
    
    this.pendingItems = [];
    this.pendingPromises = [];
    this.timeoutId = null;

    try {
      const results = await this.loadFn(items);
      promises.forEach(({ resolve }) => resolve(results));
    } catch (error) {
      promises.forEach(({ reject }) => reject(error));
    }
  }
}

/**
 * 创建加载状态Hook的工厂函数
 */
export function createLoadingHook(key: string) {
  const manager = LoadingManager.getInstance();
  
  return function useLoading() {
    const [status, setStatus] = React.useState<LoadingStatus>(
      manager.getLoadingState(key)
    );

    React.useEffect(() => {
      const unsubscribe = manager.subscribe(key, setStatus);
      return unsubscribe;
    }, []);

    const setLoading = React.useCallback((
      state: LoadingState, 
      message?: string, 
      progress?: number
    ) => {
      manager.setLoadingState(key, state, message, progress);
    }, []);

    return {
      ...status,
      setLoading,
      isLoading: status.state === 'loading',
      isError: status.state === 'error',
      isSuccess: status.state === 'success',
      duration: manager.getLoadingDuration(key)
    };
  };
}

// 为了兼容性，添加React导入
import React from 'react';
