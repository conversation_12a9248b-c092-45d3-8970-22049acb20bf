'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Edit, Trash2, Settings, Search } from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import { Activity } from '@/types';
import { ActivityForm } from './ActivityForm';
import { DeleteConfirmDialog } from './DeleteConfirmDialog';

interface ActivityManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ActivityManagementModal({ isOpen, onClose }: ActivityManagementModalProps) {
  const { activities, deleteActivity } = useAppStore();
  const [currentView, setCurrentView] = useState<'list' | 'form' | 'delete'>('list');
  const [editingActivity, setEditingActivity] = useState<Activity | null>(null);
  const [deletingActivity, setDeletingActivity] = useState<Activity | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // 分组活动：预设活动 vs 自定义活动
  const presetActivityIds = [
    '深度工作', '会议', '学习', '运动', '休息', '娱乐', '通勤', '用餐'
  ];

  // 搜索过滤功能
  const filteredActivities = activities.filter(activity =>
    activity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (activity.description && activity.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const presetActivities = filteredActivities.filter(activity =>
    presetActivityIds.some(name => activity.name === name)
  );

  const customActivities = filteredActivities.filter(activity =>
    !presetActivityIds.some(name => activity.name === name)
  );

  // 处理创建新活动
  const handleCreateActivity = () => {
    setEditingActivity(null);
    setCurrentView('form');
  };

  // 处理编辑活动
  const handleEditActivity = (activity: Activity) => {
    setEditingActivity(activity);
    setCurrentView('form');
  };

  // 处理删除活动
  const handleDeleteActivity = (activity: Activity) => {
    setDeletingActivity(activity);
    setCurrentView('delete');
  };

  // 确认删除活动
  const handleConfirmDelete = () => {
    if (deletingActivity) {
      deleteActivity(deletingActivity.id);
      setDeletingActivity(null);
      setCurrentView('list');
    }
  };

  // 返回列表视图
  const handleBackToList = () => {
    setCurrentView('list');
    setEditingActivity(null);
    setDeletingActivity(null);
  };

  // 阻止事件冒泡
  const handleModalClick = (event: React.MouseEvent) => {
    event.stopPropagation();
  };

  // 键盘快捷键支持
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // ESC键关闭模态框
      if (event.key === 'Escape') {
        if (currentView === 'list') {
          onClose();
        } else {
          handleBackToList();
        }
        return;
      }

      // Ctrl+N 创建新活动
      if ((event.ctrlKey || event.metaKey) && event.key === 'n' && currentView === 'list') {
        event.preventDefault();
        handleCreateActivity();
        return;
      }

      // Ctrl+K 或 Command+K 聚焦搜索框
      if ((event.ctrlKey || event.metaKey) && event.key === 'k' && currentView === 'list') {
        event.preventDefault();
        const searchInput = document.querySelector('input[placeholder="搜索活动..."]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, currentView, onClose]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="relative w-full max-w-5xl max-h-[90vh] bg-white rounded-xl shadow-2xl overflow-hidden flex flex-col"
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ type: "spring", stiffness: 300, damping: 25 }}
          onClick={handleModalClick}
        >
          {/* 模态框头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <Settings className="w-6 h-6 text-gray-600" />
              <h2 className="text-xl font-semibold text-gray-900">
                {currentView === 'form' ? (editingActivity ? '编辑活动' : '创建活动') : '活动管理'}
              </h2>
            </div>
            <div className="flex items-center space-x-2">
              {currentView === 'list' && (
                <div className="relative">
                  <kbd className="absolute left-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400">⌘K</kbd>
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="搜索活动..."
                    className="pl-10 pr-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-48"
                  />
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                </div>
              )}
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* 模态框内容 */}
          <div className="flex-1 overflow-y-auto">
            {currentView === 'list' && (
              <ActivityListView
                presetActivities={presetActivities}
                customActivities={customActivities}
                onCreateActivity={handleCreateActivity}
                onEditActivity={handleEditActivity}
                onDeleteActivity={handleDeleteActivity}
                searchTerm={searchTerm}
              />
            )}

            {currentView === 'form' && (
              <ActivityForm
                activity={editingActivity}
                onSave={handleBackToList}
                onCancel={handleBackToList}
              />
            )}

            {currentView === 'delete' && deletingActivity && (
              <DeleteConfirmDialog
                activity={deletingActivity}
                onConfirm={handleConfirmDelete}
                onCancel={handleBackToList}
              />
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

// 活动列表视图组件
interface ActivityListViewProps {
  presetActivities: Activity[];
  customActivities: Activity[];
  onCreateActivity: () => void;
  onEditActivity: (activity: Activity) => void;
  onDeleteActivity: (activity: Activity) => void;
  searchTerm: string; // 添加searchTerm属性
}

function ActivityListView({
  presetActivities,
  customActivities,
  onCreateActivity,
  onEditActivity,
  onDeleteActivity,
  searchTerm
}: ActivityListViewProps) {
  return (
    <div className="p-6 space-y-6">
      {/* 创建新活动按钮 */}
      <motion.button
        onClick={onCreateActivity}
        className="w-full flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors group"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Plus className="w-5 h-5 text-gray-400 group-hover:text-blue-500" />
        <span className="text-gray-600 group-hover:text-blue-600 font-medium">创建新活动</span>
      </motion.button>

      {/* 预设活动组 */}
      <ActivityGroup
        title="预设活动"
        description="系统内置的常用活动类型"
        activities={presetActivities}
        onEditActivity={onEditActivity}
        onDeleteActivity={onDeleteActivity}
        allowDelete={true}
        searchTerm={searchTerm}
      />

      {/* 自定义活动组 */}
      <ActivityGroup
        title="自定义活动"
        description="您创建的个性化活动类型"
        activities={customActivities}
        onEditActivity={onEditActivity}
        onDeleteActivity={onDeleteActivity}
        allowDelete={true}
        searchTerm={searchTerm}
      />
    </div>
  );
}

// 活动组组件
interface ActivityGroupProps {
  title: string;
  description: string;
  activities: Activity[];
  onEditActivity: (activity: Activity) => void;
  onDeleteActivity: (activity: Activity) => void;
  allowDelete: boolean;
  searchTerm: string; // 添加searchTerm属性
}

function ActivityGroup({
  title,
  description,
  activities,
  onEditActivity,
  onDeleteActivity,
  allowDelete,
  searchTerm
}: ActivityGroupProps) {
  return (
    <div className="space-y-3">
      <div>
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
      
      <div className="grid grid-cols-1 gap-3">
        {activities.map((activity) => (
          <ActivityItem
            key={activity.id}
            activity={activity}
            onEdit={() => onEditActivity(activity)}
            onDelete={() => onDeleteActivity(activity)}
            allowDelete={allowDelete}
          />
        ))}
        
        {activities.length === 0 && (
          <div className="text-center py-8 text-gray-400">
            {searchTerm ? (
              <div>
                <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>未找到匹配的活动</p>
                <p className="text-xs mt-1">尝试使用其他关键词搜索</p>
              </div>
            ) : (
              <div>
                <Plus className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>暂无{title.includes('自定义') ? '自定义' : ''}活动</p>
                {title.includes('自定义') && (
                  <p className="text-xs mt-1">点击上方"创建新活动"按钮开始添加</p>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// 活动项组件
interface ActivityItemProps {
  activity: Activity;
  onEdit: () => void;
  onDelete: () => void;
  allowDelete: boolean;
}

function ActivityItem({ activity, onEdit, onDelete, allowDelete }: ActivityItemProps) {
  return (
    <motion.div
      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
      whileHover={{ scale: 1.01 }}
      transition={{ type: "spring", stiffness: 400, damping: 25 }}
    >
      <div className="flex items-center space-x-3">
        <div
          className="w-4 h-4 rounded-full"
          style={{ backgroundColor: activity.color }}
        />
        <div>
          <h4 className="font-medium text-gray-900">{activity.name}</h4>
          {activity.description && (
            <p className="text-sm text-gray-500">{activity.description}</p>
          )}
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <button
          onClick={onEdit}
          className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
          title="编辑活动"
        >
          <Edit className="w-4 h-4" />
        </button>
        
        {allowDelete && (
          <button
            onClick={onDelete}
            className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-100 rounded-lg transition-colors"
            title="删除活动"
          >
            <Trash2 className="w-4 h-4" />
          </button>
        )}
      </div>
    </motion.div>
  );
}
