'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { type DragPath } from '@/utils/dragUtils';

interface DragPreviewProps {
  dragPath: DragPath | null;
  isVisible: boolean;
}

export function DragPreview({ dragPath, isVisible }: DragPreviewProps) {
  if (!isVisible || !dragPath) {
    return null;
  }

  const { direction, timeRange, selectedSlots } = dragPath;

  // 计算预览位置（基于第一个选中的时间槽）
  const firstSlot = selectedSlots[0];
  const row = Math.floor(firstSlot / 6);
  const col = firstSlot % 6;

  // 计算预览框的位置
  const getPreviewStyle = () => {
    const gap = 8; // var(--spacing-2)
    const blockWidth = `calc((100% - ${gap * 5}px) / 6)`;
    const blockHeight = 60;

    const leftOffset = `calc(${col} * (${blockWidth} + ${gap}px))`;
    const topOffset = row * (blockHeight + gap) - 40; // 在时间块上方显示

    return {
      left: leftOffset,
      top: `${topOffset}px`,
      position: 'absolute' as const,
      zIndex: 20
    };
  };

  // 获取方向指示颜色
  const getDirectionColor = () => {
    switch (direction.type) {
      case 'vertical':
        return 'var(--success-500)'; // 绿色 - 垂直选择
      case 'diagonal':
        return 'var(--info-500)'; // 蓝色 - 跨列选择
      default:
        return 'var(--success-500)';
    }
  };

  // 获取方向图标
  const getDirectionIcon = () => {
    switch (direction.type) {
      case 'vertical':
        return '↕️';
      case 'diagonal':
        return '↗️';
      default:
        return '↕️';
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        style={getPreviewStyle()}
        initial={{ opacity: 0, scale: 0.8, y: -10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: -10 }}
        transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
      >
        {/* 主预览卡片 */}
        <div
          style={{
            background: 'white',
            borderRadius: 'var(--radius-lg)',
            boxShadow: 'var(--shadow-lg)',
            border: `2px solid ${getDirectionColor()}`,
            padding: 'var(--spacing-3)',
            minWidth: '200px',
            fontSize: 'var(--font-size-sm)'
          }}
        >
          {/* 方向指示 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: 'var(--spacing-2)',
              marginBottom: 'var(--spacing-2)',
              color: getDirectionColor(),
              fontWeight: 'var(--font-weight-semibold)'
            }}
          >
            <span style={{ fontSize: 'var(--font-size-base)' }}>
              {getDirectionIcon()}
            </span>
            <span>{direction.type === 'vertical' ? '垂直选择' : '跨列选择'}</span>
          </div>

          {/* 时间范围 */}
          <div
            style={{
              marginBottom: 'var(--spacing-2)'
            }}
          >
            <div
              style={{
                fontFamily: 'var(--font-mono)',
                fontWeight: 'var(--font-weight-bold)',
                color: 'var(--neutral-900)',
                fontSize: 'var(--font-size-base)'
              }}
            >
              {timeRange.start} - {timeRange.end}
            </div>
            <div
              style={{
                color: 'var(--neutral-600)',
                fontSize: 'var(--font-size-xs)'
              }}
            >
              {timeRange.duration} 分钟 · {selectedSlots.length} 个时间块
            </div>
          </div>

          {/* 建议提示 */}
          {direction.suggestion && (
            <div
              style={{
                fontSize: 'var(--font-size-xs)',
                color: 'var(--success-600)',
                fontStyle: 'italic',
                borderTop: `1px solid var(--neutral-200)`,
                paddingTop: 'var(--spacing-2)',
                marginTop: 'var(--spacing-2)'
              }}
            >
              💡 {direction.suggestion}
            </div>
          )}
        </div>

        {/* 连接线指示器（对于跨行选择） */}
        {direction.type === 'diagonal' && selectedSlots.length > 1 && (
          <motion.div
            style={{
              position: 'absolute',
              top: '100%',
              left: '50%',
              transform: 'translateX(-50%)',
              marginTop: 'var(--spacing-1)'
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            <div
              style={{
                width: '2px',
                height: '20px',
                background: `linear-gradient(to bottom, ${getDirectionColor()}, transparent)`,
                borderRadius: '1px'
              }}
            />
          </motion.div>
        )}
      </motion.div>
    </AnimatePresence>
  );
}

/**
 * 拖拽路径可视化组件
 * 在时间块网格上显示拖拽路径
 */
interface DragPathVisualizerProps {
  dragPath: DragPath | null;
  isVisible: boolean;
}

export function DragPathVisualizer({ dragPath, isVisible }: DragPathVisualizerProps) {
  if (!isVisible || !dragPath || dragPath.selectedSlots.length <= 1) {
    return null;
  }

  const { selectedSlots, direction } = dragPath;

  return (
    <div
      style={{
        position: 'absolute',
        inset: 0,
        pointerEvents: 'none',
        zIndex: 5
      }}
    >
      {/* 为每个选中的时间槽添加路径指示 */}
      {selectedSlots.map((slot, index) => {
        // 统一坐标系统：按列优先布局 (6列×8行)
        // timeSlot转行列：row = timeSlot % 8, col = Math.floor(timeSlot / 8)
        const row = slot % 8;
        const col = Math.floor(slot / 8);
        const isFirst = index === 0;
        const isLast = index === selectedSlots.length - 1;

        const gap = 8;
        const blockWidth = `calc((100% - ${gap * 5}px) / 6)`;
        const blockHeight = 60;

        const leftOffset = `calc(${col} * (${blockWidth} + ${gap}px))`;
        const topOffset = row * (blockHeight + gap);

        return (
          <motion.div
            key={`path-${slot}`}
            style={{
              position: 'absolute',
              left: leftOffset,
              top: `${topOffset}px`,
              width: blockWidth,
              height: `${blockHeight}px`,
              border: `2px solid ${direction.isColumnPrimary ? 'var(--success-400)' : 'var(--warning-400)'}`,
              borderRadius: 'var(--radius-lg)',
              backgroundColor: direction.isColumnPrimary ? 'var(--success-50)' : 'var(--warning-50)',
              opacity: 0.8
            }}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 0.8, scale: 1 }}
            transition={{ duration: 0.15, delay: index * 0.02 }}
          >
            {/* 起始和结束标记 */}
            {(isFirst || isLast) && (
              <div
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: direction.isColumnPrimary ? 'var(--success-500)' : 'var(--warning-500)',
                  border: '2px solid white',
                  boxShadow: 'var(--shadow-sm)'
                }}
              />
            )}
          </motion.div>
        );
      })}
    </div>
  );
}
