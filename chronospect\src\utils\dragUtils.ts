/**
 * 拖拽工具函数
 * 为6列×8行时间轴流式布局提供智能拖拽支持
 */

export interface DragDirection {
  type: 'vertical' | 'diagonal';
  isColumnPrimary: boolean; // 是否为列优先拖拽
  suggestion?: string; // 拖拽建议
}

export interface DragPath {
  startSlot: number;
  endSlot: number;
  direction: DragDirection;
  selectedSlots: number[];
  timeRange: {
    start: string;
    end: string;
    duration: number; // 分钟数
  };
}

/**
 * 检测拖拽方向
 * @param startSlot 起始时间槽
 * @param currentSlot 当前时间槽
 * @returns 拖拽方向信息
 */
export function detectDragDirection(startSlot: number, currentSlot: number): DragDirection {
  // 新布局：6列×8行，每列代表4小时时间段
  // timeSlot到列的映射：col = Math.floor(timeSlot / 8)
  const startCol = Math.floor(startSlot / 8);
  const currentCol = Math.floor(currentSlot / 8);

  // 垂直拖拽（同一列内）
  if (startCol === currentCol) {
    return {
      type: 'vertical',
      isColumnPrimary: true,
      suggestion: '垂直拖拽 - 选择同一时间段内的连续时间'
    };
  }

  // 跨列拖拽（列优先选择）
  return {
    type: 'diagonal',
    isColumnPrimary: true,
    suggestion: '跨列拖拽 - 列优先选择连续时间'
  };
}

/**
 * 计算拖拽路径和选中的时间槽
 * @param startSlot 起始时间槽
 * @param endSlot 结束时间槽
 * @returns 拖拽路径信息
 */
export function calculateDragPath(startSlot: number, endSlot: number): DragPath {
  const direction = detectDragDirection(startSlot, endSlot);
  const selectedSlots = getSelectedTimeSlots(startSlot, endSlot);
  const timeRange = calculateTimeRange(selectedSlots);

  return {
    startSlot,
    endSlot,
    direction,
    selectedSlots,
    timeRange
  };
}

/**
 * 获取选中的时间槽数组（列优先选择算法）
 * @param startSlot 起始时间槽
 * @param endSlot 结束时间槽
 * @returns 选中的时间槽数组
 */
export function getSelectedTimeSlots(startSlot: number, endSlot: number): number[] {
  const direction = detectDragDirection(startSlot, endSlot);

  if (direction.type === 'vertical') {
    return getVerticalSelection(startSlot, endSlot);
  } else {
    return getColumnPrioritySelection(startSlot, endSlot);
  }
}

/**
 * 垂直拖拽选择（同一列内）
 * @param startSlot 起始时间槽
 * @param endSlot 结束时间槽
 * @returns 同一列内的时间槽数组
 */
function getVerticalSelection(startSlot: number, endSlot: number): number[] {
  // 新布局：6列×8行，每列代表4小时时间段
  // 同一列内的timeSlot：col * 8 + row (row: 0-7)
  const column = Math.floor(startSlot / 8);
  const startRow = startSlot % 8;
  const endRow = endSlot % 8;

  const minRow = Math.min(startRow, endRow);
  const maxRow = Math.max(startRow, endRow);

  const selectedSlots: number[] = [];

  for (let row = minRow; row <= maxRow; row++) {
    const timeSlot = column * 8 + row;
    if (timeSlot < 48) { // 确保不超出48个时间槽的范围
      selectedSlots.push(timeSlot);
    }
  }

  return selectedSlots;
}

/**
 * 跨列拖拽选择算法（保持真实时间连续性）
 * @param startSlot 起始时间槽
 * @param endSlot 结束时间槽
 * @returns 真实时间连续的时间槽数组
 */
function getColumnPrioritySelection(startSlot: number, endSlot: number): number[] {
  // 将timeSlot转换为实际时间
  const startTime = timeSlotToTime(startSlot);
  const endTime = timeSlotToTime(endSlot);

  // 确定时间范围的起始和结束
  const earlierTime = compareTime(startTime, endTime) <= 0 ? startTime : endTime;
  const laterTime = compareTime(startTime, endTime) <= 0 ? endTime : startTime;

  // 生成从起始时间到结束时间的所有30分钟间隔
  const selectedSlots: number[] = [];
  let currentHour = earlierTime.hour;
  let currentMinute = earlierTime.minute;

  while (compareTime({ hour: currentHour, minute: currentMinute }, laterTime) <= 0) {
    const timeSlot = timeToTimeSlot(currentHour, currentMinute);
    selectedSlots.push(timeSlot);

    // 移动到下一个30分钟间隔
    currentMinute += 30;
    if (currentMinute >= 60) {
      currentMinute = 0;
      currentHour++;
    }

    // 防止无限循环，确保不超过24小时
    if (currentHour >= 24) {
      break;
    }
  }

  return selectedSlots;
}

/**
 * 将timeSlot转换为实际时间（小时:分钟）
 * @param timeSlot 时间槽索引 (0-47)
 * @returns {hour, minute} 时间对象
 */
function timeSlotToTime(timeSlot: number): { hour: number; minute: number } {
  const hour = Math.floor(timeSlot / 2);
  const minute = (timeSlot % 2) * 30;
  return { hour, minute };
}

/**
 * 将时间（小时:分钟）转换为timeSlot
 * @param hour 小时 (0-23)
 * @param minute 分钟 (0, 30)
 * @returns timeSlot索引
 */
function timeToTimeSlot(hour: number, minute: number): number {
  return hour * 2 + (minute === 30 ? 1 : 0);
}

/**
 * 比较两个时间的大小
 * @param time1 时间1
 * @param time2 时间2
 * @returns -1: time1 < time2, 0: time1 = time2, 1: time1 > time2
 */
function compareTime(time1: { hour: number; minute: number }, time2: { hour: number; minute: number }): number {
  if (time1.hour !== time2.hour) {
    return time1.hour - time2.hour;
  }
  return time1.minute - time2.minute;
}

/**
 * 计算时间范围
 * @param timeSlots 时间槽数组
 * @returns 时间范围信息
 */
export function calculateTimeRange(timeSlots: number[]): {
  start: string;
  end: string;
  duration: number;
} {
  if (timeSlots.length === 0) {
    return { start: '', end: '', duration: 0 };
  }

  const sortedSlots = [...timeSlots].sort((a, b) => a - b);
  const startSlot = sortedSlots[0];
  const endSlot = sortedSlots[sortedSlots.length - 1];

  // 计算开始时间
  const startHour = Math.floor(startSlot / 2);
  const startMinute = (startSlot % 2) * 30;
  const startTime = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;

  // 计算结束时间（下一个时间槽的开始时间）
  const endSlotNext = endSlot + 1;
  const endHour = Math.floor(endSlotNext / 2);
  const endMinute = (endSlotNext % 2) * 30;
  const endTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;

  // 计算持续时间（分钟）
  const duration = timeSlots.length * 30;

  return {
    start: startTime,
    end: endTime,
    duration
  };
}

/**
 * 检查拖拽是否为推荐的列优先方式
 * @param startSlot 起始时间槽
 * @param endSlot 结束时间槽
 * @returns 是否为推荐方式（现在所有拖拽都是列优先）
 */
export function isRecommendedDragPattern(startSlot: number, endSlot: number): boolean {
  // 所有拖拽都是列优先，因为我们移除了水平拖拽
  return true;
}

/**
 * 获取拖拽建议文本
 * @param startSlot 起始时间槽
 * @param endSlot 结束时间槽
 * @returns 建议文本
 */
export function getDragSuggestion(startSlot: number, endSlot: number): string {
  const direction = detectDragDirection(startSlot, endSlot);
  return direction.suggestion || '';
}

/**
 * 计算网格位置信息
 * @param timeSlot 时间槽
 * @returns 网格位置
 */
export function getGridPosition(timeSlot: number): { row: number; col: number } {
  return {
    row: Math.floor(timeSlot / 6),
    col: timeSlot % 6
  };
}

/**
 * 检查两个时间槽是否在同一列
 * @param slot1 时间槽1
 * @param slot2 时间槽2
 * @returns 是否在同一列
 */
export function isSameColumn(slot1: number, slot2: number): boolean {
  return (slot1 % 6) === (slot2 % 6);
}

/**
 * 检查两个时间槽是否在同一行
 * @param slot1 时间槽1
 * @param slot2 时间槽2
 * @returns 是否在同一行
 */
export function isSameRow(slot1: number, slot2: number): boolean {
  return Math.floor(slot1 / 6) === Math.floor(slot2 / 6);
}

/**
 * 获取列优先选择的描述文本
 * @param startSlot 起始时间槽
 * @param endSlot 结束时间槽
 * @returns 描述文本
 */
export function getSelectionDescription(startSlot: number, endSlot: number): string {
  const direction = detectDragDirection(startSlot, endSlot);
  const selectedSlots = getSelectedTimeSlots(startSlot, endSlot);

  if (direction.type === 'vertical') {
    const column = Math.floor(startSlot / 8);
    const timeSegments = ['深夜', '黎明', '上午', '下午', '傍晚', '夜晚'];
    const segmentName = timeSegments[column] || `第${column + 1}列`;
    return `${segmentName}时间段内的垂直选择`;
  } else {
    const startCol = Math.floor(startSlot / 8);
    const endCol = Math.floor(endSlot / 8);
    const colCount = Math.abs(endCol - startCol) + 1;
    return `跨${colCount}个时间段的列优先选择`;
  }
}
