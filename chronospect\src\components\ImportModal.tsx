import React, { useState, useRef, useCallback } from 'react';
import PortalModal from './PortalModal';

interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (file: File, strategy: ImportStrategy) => Promise<void>;
}

export type ImportStrategy = 'merge' | 'overwrite' | 'append';

interface ImportState {
  file: File | null;
  strategy: ImportStrategy;
  isImporting: boolean;
  error: string | null;
  success: boolean;
  importResult?: {
    activitiesAdded: number;
    timeBlocksAdded: number;
  };
}

const ImportModal: React.FC<ImportModalProps> = ({
  isOpen,
  onClose,
  onImport
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [importState, setImportState] = useState<ImportState>({
    file: null,
    strategy: 'merge',
    isImporting: false,
    error: null,
    success: false
  });

  // 重置状态
  const resetState = useCallback(() => {
    setImportState({
      file: null,
      strategy: 'merge',
      isImporting: false,
      error: null,
      success: false
    });
    // 重置文件选择器
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  // 处理模态框关闭
  const handleClose = useCallback(() => {
    if (importState.isImporting) return; // 导入中不允许关闭
    resetState();
    onClose();
  }, [importState.isImporting, resetState, onClose]);

  // 处理文件选择
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImportState(prev => ({
        ...prev,
        file,
        error: null,
        success: false
      }));
    }
  }, []);

  // 处理策略选择
  const handleStrategyChange = useCallback((strategy: ImportStrategy) => {
    setImportState(prev => ({ ...prev, strategy }));
  }, []);

  // 处理导入确认
  const handleImportConfirm = useCallback(async () => {
    if (!importState.file) return;

    setImportState(prev => ({ ...prev, isImporting: true, error: null }));

    try {
      await onImport(importState.file, importState.strategy);
      setImportState(prev => ({
        ...prev,
        isImporting: false,
        success: true,
        error: null
      }));

      // 导入成功后2.5秒自动关闭模态框
      setTimeout(() => {
        resetState();
        onClose();
      }, 2500);
    } catch (error) {
      setImportState(prev => ({
        ...prev,
        isImporting: false,
        error: error instanceof Error ? error.message : '导入失败'
      }));
    }
  }, [importState.file, importState.strategy, onImport, resetState, onClose]);

  // 触发文件选择
  const triggerFileSelect = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const strategyOptions = [
    { value: 'merge' as const, label: '智能合并（推荐）', description: '合并新数据，保留现有数据' },
    { value: 'overwrite' as const, label: '完全覆盖', description: '清空现有数据，使用导入数据' },
    { value: 'append' as const, label: '仅添加新数据', description: '只添加不冲突的新数据' }
  ];

  return (
    <PortalModal
      isOpen={isOpen}
      onClose={handleClose}
      title="导入数据"
      className="max-w-lg"
      closeOnBackdropClick={!importState.isImporting}
      closeOnEscape={!importState.isImporting}
    >
      <div className="p-6">
        {/* 文件选择区域 */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            选择文件
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
            {importState.file ? (
              <div className="flex items-center justify-center space-x-2">
                <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm text-gray-700">{importState.file.name}</span>
                <button
                  onClick={triggerFileSelect}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                  disabled={importState.isImporting}
                >
                  重新选择
                </button>
              </div>
            ) : (
              <div>
                <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <button
                  onClick={triggerFileSelect}
                  className="text-blue-600 hover:text-blue-800 font-medium"
                  disabled={importState.isImporting}
                >
                  点击选择文件
                </button>
                <p className="text-xs text-gray-500 mt-1">支持 JSON 格式</p>
              </div>
            )}
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>

        {/* 导入策略选择 */}
        {importState.file && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              导入策略
            </label>
            <div className="space-y-2">
              {strategyOptions.map((option) => (
                <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="strategy"
                    value={option.value}
                    checked={importState.strategy === option.value}
                    onChange={() => handleStrategyChange(option.value)}
                    className="mt-1 text-blue-600 focus:ring-blue-500"
                    disabled={importState.isImporting}
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900">{option.label}</div>
                    <div className="text-xs text-gray-500">{option.description}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {importState.error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <svg className="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-red-800">导入失败</h3>
                <p className="text-sm text-red-700 mt-1">{importState.error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 成功信息 */}
        {importState.success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="flex">
              <svg className="w-5 h-5 text-green-400 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h3 className="text-sm font-medium text-green-800">导入成功</h3>
                <p className="text-sm text-green-700 mt-1">数据导入成功</p>
                {importState.importResult && (
                  <p className="text-xs text-green-600 mt-1">
                    活动: +{importState.importResult.activitiesAdded} 时间块: +{importState.importResult.timeBlocksAdded}
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        {!importState.success && (
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              disabled={importState.isImporting}
            >
              取消
            </button>
            <button
              onClick={handleImportConfirm}
              disabled={!importState.file || importState.isImporting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {importState.isImporting ? (
                <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  导入中...
                </div>
              ) : (
                '确认导入'
              )}
            </button>
          </div>
        )}

        {/* 导入成功时显示自动关闭提示 */}
        {importState.success && (
          <div className="text-center">
            <p className="text-sm text-gray-600">
              导入成功！模态框将在几秒后自动关闭...
            </p>
          </div>
        )}
      </div>
    </PortalModal>
  );
};

export default ImportModal;
