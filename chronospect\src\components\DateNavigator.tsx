'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Home, Settings } from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import { formatDate } from '@/utils/timeUtils';
import { CustomDatePicker } from './CustomDatePicker';
import { useIsClient } from '@/hooks/useIsClient';

interface DateNavigatorProps {
  onManageActivities?: () => void;
}

export function DateNavigator({ onManageActivities }: DateNavigatorProps = {}) {
  const {
    currentDate,
    currentTime,
    goToPreviousDay,
    goToNextDay,
    goToToday,
    setCurrentDate
  } = useAppStore();

  const isClient = useIsClient();

  // 使用store中的响应式时间状态，避免SSR hydration mismatch
  const isToday = isClient ? currentDate === currentTime.date : false;
  const formattedDate = formatDate(currentDate);

  // 处理日期输入变化
  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = event.target.value;
    if (newDate) {
      setCurrentDate(newDate);
    }
  };

  return (
    <div className="flex items-center justify-center mb-8" style={{ gap: 'var(--spacing-4)' }}>
      {/* 上一天按钮 */}
      <motion.button
        onClick={goToPreviousDay}
        className="btn-secondary"
        style={{
          padding: 'var(--spacing-3)',
          borderRadius: 'var(--radius-full)',
          background: 'white',
          boxShadow: 'var(--shadow-md)',
          border: '1px solid var(--neutral-200)'
        }}
        whileHover={{
          scale: 1.05,
          boxShadow: 'var(--shadow-lg)',
          borderColor: 'var(--neutral-300)'
        }}
        whileTap={{ scale: 0.95 }}
        transition={{ duration: 0.15 }}
      >
        <ChevronLeft size={20} style={{ color: 'var(--neutral-600)' }} />
      </motion.button>

      {/* 日期显示和选择器 */}
      <div className="flex items-center" style={{ gap: 'var(--spacing-3)' }}>
        {/* 美观的日期选择器 */}
        <CustomDatePicker
          value={currentDate}
          onChange={setCurrentDate}
        />

        {/* 回到今天按钮 */}
        {!isToday && (
          <motion.button
            onClick={goToToday}
            className="btn-primary flex items-center"
            style={{
              gap: 'var(--spacing-2)',
              padding: 'var(--spacing-3) var(--spacing-4)',
              background: 'var(--primary-500)',
              color: 'white',
              borderRadius: 'var(--radius-lg)',
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)'
            }}
            whileHover={{
              scale: 1.05,
              background: 'var(--primary-600)',
              boxShadow: 'var(--shadow-md)'
            }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 10 }}
            transition={{ duration: 0.15 }}
          >
            <Home size={16} />
            <span>今天</span>
          </motion.button>
        )}

        {/* 活动管理按钮 */}
        {onManageActivities && (
          <motion.button
            onClick={onManageActivities}
            className="btn-secondary flex items-center"
            style={{
              gap: 'var(--spacing-2)',
              padding: 'var(--spacing-3) var(--spacing-4)',
              background: 'white',
              color: 'var(--neutral-700)',
              borderRadius: 'var(--radius-lg)',
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)',
              border: '1px solid var(--neutral-200)',
              boxShadow: 'var(--shadow-md)'
            }}
            whileHover={{
              scale: 1.05,
              background: 'var(--neutral-50)',
              borderColor: 'var(--neutral-300)',
              boxShadow: 'var(--shadow-lg)'
            }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 10 }}
            transition={{ duration: 0.15 }}
            title="管理活动"
          >
            <Settings size={16} />
            <span>活动管理</span>
          </motion.button>
        )}
      </div>

      {/* 下一天按钮 */}
      <motion.button
        onClick={goToNextDay}
        className="btn-secondary"
        style={{
          padding: 'var(--spacing-3)',
          borderRadius: 'var(--radius-full)',
          background: 'white',
          boxShadow: 'var(--shadow-md)',
          border: '1px solid var(--neutral-200)'
        }}
        whileHover={{
          scale: 1.05,
          boxShadow: 'var(--shadow-lg)',
          borderColor: 'var(--neutral-300)'
        }}
        whileTap={{ scale: 0.95 }}
        transition={{ duration: 0.15 }}
      >
        <ChevronRight size={20} style={{ color: 'var(--neutral-600)' }} />
      </motion.button>
    </div>
  );
}
