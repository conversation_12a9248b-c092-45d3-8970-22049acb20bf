'use client';

import React, { useEffect, useState } from 'react';

interface HydrationBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * HydrationBoundary组件
 * 用于防止SSR hydration mismatch错误
 * 确保子组件只在客户端完全hydration后才渲染
 */
export function HydrationBoundary({ children, fallback = null }: HydrationBoundaryProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // 确保在客户端hydration完成后才渲染子组件
    setIsHydrated(true);
  }, []);

  // 在hydration完成之前，渲染fallback或null
  if (!isHydrated) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}
