'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { DailyStats, ActivityStats } from '@/types';
import { formatDuration } from '@/utils/timeUtils';
import { getTimeRangesText, calculateEfficiencyScore } from '@/utils/statsUtils';
import * as LucideIcons from 'lucide-react';

interface ActivityListProps {
  stats: DailyStats;
}

export function ActivityList({ stats }: ActivityListProps) {
  // 如果没有活动数据，显示空状态
  if (stats.activities.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          📋 活动详情
        </h3>
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📝</div>
          <p className="text-gray-500">暂无活动记录</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 活动排行榜 */}
      <motion.div
        className="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          🏆 活动排行榜
        </h3>
        <div className="space-y-3">
          {stats.activities.map((activity, index) => (
            <ActivityRankItem
              key={activity.activityId}
              activity={activity}
              rank={index + 1}
              totalFilledMinutes={stats.filledMinutes}
            />
          ))}
        </div>
      </motion.div>

      {/* 时间段详情 */}
      <motion.div
        className="bg-white rounded-xl shadow-lg border border-gray-200 p-6"
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          ⏰ 时间段详情
        </h3>
        <div className="space-y-4">
          {stats.activities.map((activity) => (
            <ActivityTimeDetail
              key={activity.activityId}
              activity={activity}
            />
          ))}
        </div>
      </motion.div>
    </div>
  );
}

// 活动排行项组件
interface ActivityRankItemProps {
  activity: ActivityStats;
  rank: number;
  totalFilledMinutes: number;
}

function ActivityRankItem({ activity, rank, totalFilledMinutes }: ActivityRankItemProps) {
  const percentage = totalFilledMinutes > 0 ? (activity.totalMinutes / totalFilledMinutes) * 100 : 0;
  
  // 获取排名图标
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `${rank}`;
    }
  };

  return (
    <motion.div
      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
      whileHover={{ scale: 1.02 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <div className="flex items-center space-x-4">
        {/* 排名 */}
        <div className="text-2xl font-bold w-8 text-center">
          {getRankIcon(rank)}
        </div>
        
        {/* 活动信息 */}
        <div className="flex items-center space-x-3">
          <div
            className="w-4 h-4 rounded-full"
            style={{ backgroundColor: activity.color }}
          />
          <div>
            <div className="font-medium text-gray-900">
              {activity.activityName}
            </div>
            <div className="text-sm text-gray-500">
              {activity.timeRanges.length} 个时间段
            </div>
          </div>
        </div>
      </div>

      {/* 时间和百分比 */}
      <div className="text-right">
        <div className="font-semibold text-gray-900">
          {formatDuration(activity.totalMinutes)}
        </div>
        <div className="text-sm text-gray-500">
          {percentage.toFixed(1)}%
        </div>
      </div>
    </motion.div>
  );
}

// 活动时间详情组件
interface ActivityTimeDetailProps {
  activity: ActivityStats;
}

function ActivityTimeDetail({ activity }: ActivityTimeDetailProps) {
  const efficiencyScore = calculateEfficiencyScore(activity.timeRanges, activity.totalMinutes);
  
  return (
    <div className="border border-gray-200 rounded-lg p-4">
      {/* 活动标题 */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: activity.color }}
          />
          <h4 className="font-medium text-gray-900">
            {activity.activityName}
          </h4>
        </div>
        <div className="text-sm text-gray-500">
          {formatDuration(activity.totalMinutes)}
        </div>
      </div>

      {/* 时间段列表 */}
      <div className="space-y-2">
        {activity.timeRanges.map((range, index) => (
          <div
            key={index}
            className="flex items-center justify-between text-sm bg-gray-50 rounded px-3 py-2"
          >
            <span className="text-gray-700">
              {range.startTime} - {range.endTime}
            </span>
            <span className="text-gray-500">
              {formatDuration(range.duration)}
            </span>
          </div>
        ))}
      </div>

      {/* 效率指标 */}
      <div className="mt-3 pt-3 border-t border-gray-100">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">连续性评分</span>
          <div className="flex items-center space-x-2">
            <div className="w-16 bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-gradient-to-r from-yellow-400 to-green-500"
                style={{ width: `${efficiencyScore}%` }}
              />
            </div>
            <span className="text-gray-700 font-medium">
              {efficiencyScore.toFixed(0)}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
