'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Download, FileText, Database, TestTube, CheckCircle, AlertCircle } from 'lucide-react';
import { downloadFile } from '@/utils/dataExport';

interface TestResult {
  method: string;
  success: boolean;
  message: string;
  timestamp: string;
}

export function DownloadTestPanel() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  const addTestResult = (method: string, success: boolean, message: string) => {
    const result: TestResult = {
      method,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]); // 保留最近10条记录
  };

  // 测试JSON下载
  const testJSONDownload = () => {
    try {
      const testData = {
        version: '1.0.0',
        exportDate: new Date().toISOString(),
        testData: {
          message: 'This is a test JSON file from Chronospect',
          timestamp: Date.now(),
          items: ['item1', 'item2', 'item3']
        }
      };
      
      const jsonContent = JSON.stringify(testData, null, 2);
      const fileName = `chronospect_test_${new Date().toISOString().split('T')[0]}.json`;
      
      downloadFile(jsonContent, fileName, 'application/json');
      addTestResult('JSON下载', true, `测试文件: ${fileName}`);
    } catch (error) {
      addTestResult('JSON下载', false, `错误: ${(error as Error).message}`);
    }
  };

  // 测试CSV下载
  const testCSVDownload = () => {
    try {
      const csvContent = [
        '日期,时间槽,开始时间,结束时间,活动名称,持续时间(分钟)',
        '2025-07-28,1,00:00,00:30,测试活动1,30',
        '2025-07-28,2,00:30,01:00,测试活动2,30',
        '2025-07-28,3,01:00,01:30,测试活动3,30'
      ].join('\n');
      
      const fileName = `chronospect_test_${new Date().toISOString().split('T')[0]}.csv`;
      
      downloadFile(csvContent, fileName, 'text/csv');
      addTestResult('CSV下载', true, `测试文件: ${fileName}`);
    } catch (error) {
      addTestResult('CSV下载', false, `错误: ${(error as Error).message}`);
    }
  };

  // 测试小文件下载
  const testSmallFileDownload = () => {
    try {
      const content = 'Hello, Chronospect! This is a test file.';
      const fileName = `chronospect_small_test_${Date.now()}.txt`;
      
      downloadFile(content, fileName, 'text/plain');
      addTestResult('小文件下载', true, `测试文件: ${fileName}`);
    } catch (error) {
      addTestResult('小文件下载', false, `错误: ${(error as Error).message}`);
    }
  };

  // 清除测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  if (!isVisible) {
    return (
      <motion.button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 bg-blue-500 text-white p-3 rounded-full shadow-lg z-50"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="打开下载测试面板"
      >
        <TestTube size={20} />
      </motion.button>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, x: -300 }}
      animate={{ opacity: 1, x: 0 }}
      className="fixed left-4 top-1/2 transform -translate-y-1/2 bg-white rounded-lg shadow-xl border p-4 w-80 max-h-96 overflow-y-auto z-50"
      style={{
        borderColor: 'var(--neutral-200)',
        boxShadow: 'var(--shadow-xl)'
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 style={{
          fontSize: 'var(--font-size-lg)',
          fontWeight: 'var(--font-weight-semibold)',
          color: 'var(--neutral-900)'
        }}>
          下载功能测试
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-500 hover:text-gray-700"
        >
          ✕
        </button>
      </div>

      <div className="space-y-2 mb-4">
        <motion.button
          onClick={testJSONDownload}
          className="w-full flex items-center justify-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Database size={16} className="mr-2" />
          测试 JSON 下载
        </motion.button>

        <motion.button
          onClick={testCSVDownload}
          className="w-full flex items-center justify-center px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <FileText size={16} className="mr-2" />
          测试 CSV 下载
        </motion.button>

        <motion.button
          onClick={testSmallFileDownload}
          className="w-full flex items-center justify-center px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Download size={16} className="mr-2" />
          测试小文件下载
        </motion.button>

        <motion.button
          onClick={clearResults}
          className="w-full px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          清除结果
        </motion.button>
      </div>

      <div className="border-t pt-3" style={{ borderColor: 'var(--neutral-200)' }}>
        <h4 style={{
          fontSize: 'var(--font-size-sm)',
          fontWeight: 'var(--font-weight-medium)',
          color: 'var(--neutral-700)',
          marginBottom: 'var(--spacing-2)'
        }}>
          测试结果:
        </h4>

        {testResults.length === 0 ? (
          <p style={{
            fontSize: 'var(--font-size-xs)',
            color: 'var(--neutral-500)',
            textAlign: 'center',
            padding: 'var(--spacing-2)'
          }}>
            暂无测试结果
          </p>
        ) : (
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-2 rounded text-xs ${
                  result.success 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}
              >
                <div className="flex items-center mb-1">
                  {result.success ? (
                    <CheckCircle size={12} className="text-green-500 mr-1" />
                  ) : (
                    <AlertCircle size={12} className="text-red-500 mr-1" />
                  )}
                  <span className={`font-medium ${
                    result.success ? 'text-green-700' : 'text-red-700'
                  }`}>
                    {result.method}
                  </span>
                  <span className="ml-auto text-gray-500">
                    {result.timestamp}
                  </span>
                </div>
                <p className={result.success ? 'text-green-600' : 'text-red-600'}>
                  {result.message}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-3 pt-3 border-t" style={{ borderColor: 'var(--neutral-200)' }}>
        <p style={{
          fontSize: 'var(--font-size-xs)',
          color: 'var(--neutral-500)',
          lineHeight: '1.4'
        }}>
          💡 提示: 请检查浏览器的下载文件夹 (通常是 Downloads) 来确认文件是否成功下载。
        </p>
      </div>
    </motion.div>
  );
}
