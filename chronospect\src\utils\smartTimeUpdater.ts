/**
 * 智能精确时间触发器
 * 基于业界最佳实践实现的高精度时间更新机制
 * 
 * 核心特性：
 * - 精确分钟边界触发
 * - 页面可见性感知
 * - 递归setTimeout机制
 * - 错误恢复和重新同步
 * - 性能监控和调试支持
 */

import { 
  calculateNextMinuteDelay, 
  calculateNextTimeSlotDelay, 
  getHighPrecisionTimestamp,
  validateTriggerPrecision 
} from './timeUtils';

export interface SmartTimeUpdaterOptions {
  /** 时间更新回调函数 */
  onTimeUpdate: () => void;
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 是否使用时间槽边界触发（30分钟），默认为分钟边界触发 */
  useTimeSlotBoundary?: boolean;
  /** 错误恢复检查间隔（毫秒），默认5分钟 */
  recoveryCheckInterval?: number;
  /** 页面可见性变化时是否立即同步，默认true */
  syncOnVisibilityChange?: boolean;
}

export interface TriggerStats {
  /** 总触发次数 */
  totalTriggers: number;
  /** 精确触发次数（100ms内） */
  accurateTriggers: number;
  /** 平均偏差（毫秒） */
  averageDeviation: number;
  /** 最大偏差（毫秒） */
  maxDeviation: number;
  /** 最后触发时间 */
  lastTriggerTime: number;
  /** 页面隐藏期间跳过的触发次数 */
  skippedTriggers: number;
}

/**
 * 智能时间更新器类
 * 实现精确的时间边界触发机制
 */
export class SmartTimeUpdater {
  private options: Required<SmartTimeUpdaterOptions>;
  private timeoutId: number | null = null;
  private recoveryIntervalId: number | null = null;
  private isRunning = false;
  private isPageVisible = true;
  private stats: TriggerStats;
  private lastExpectedTriggerTime = 0;

  constructor(options: SmartTimeUpdaterOptions) {
    this.options = {
      debug: false,
      useTimeSlotBoundary: false,
      recoveryCheckInterval: 5 * 60 * 1000, // 5分钟
      syncOnVisibilityChange: true,
      ...options
    };

    this.stats = {
      totalTriggers: 0,
      accurateTriggers: 0,
      averageDeviation: 0,
      maxDeviation: 0,
      lastTriggerTime: 0,
      skippedTriggers: 0
    };

    this.setupVisibilityListener();
  }

  /**
   * 启动智能时间更新器
   */
  start(): void {
    if (this.isRunning) {
      this.log('SmartTimeUpdater already running');
      return;
    }

    this.isRunning = true;
    this.log('Starting SmartTimeUpdater');
    
    // 立即执行一次更新
    this.options.onTimeUpdate();
    
    // 开始调度下一次更新
    this.scheduleNextUpdate();
    
    // 启动错误恢复检查
    this.startRecoveryCheck();
  }

  /**
   * 停止智能时间更新器
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    this.log('Stopping SmartTimeUpdater');
    
    // 清理定时器
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    if (this.recoveryIntervalId !== null) {
      clearInterval(this.recoveryIntervalId);
      this.recoveryIntervalId = null;
    }
  }

  /**
   * 获取触发统计信息
   */
  getStats(): TriggerStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalTriggers: 0,
      accurateTriggers: 0,
      averageDeviation: 0,
      maxDeviation: 0,
      lastTriggerTime: 0,
      skippedTriggers: 0
    };
  }

  /**
   * 调度下一次更新
   */
  private scheduleNextUpdate(): void {
    if (!this.isRunning) {
      return;
    }

    // 计算到下一个边界的延迟
    const delay = this.options.useTimeSlotBoundary 
      ? calculateNextTimeSlotDelay() 
      : calculateNextMinuteDelay();

    // 记录期望的触发时间
    this.lastExpectedTriggerTime = getHighPrecisionTimestamp() + delay;

    this.log(`Scheduling next update in ${delay}ms`);

    // 使用递归setTimeout实现精确调度
    this.timeoutId = window.setTimeout(() => {
      this.handleTrigger();
    }, delay);
  }

  /**
   * 处理时间触发
   */
  private handleTrigger(): void {
    const actualTriggerTime = getHighPrecisionTimestamp();
    
    // 验证触发精度
    if (this.lastExpectedTriggerTime > 0) {
      const precision = validateTriggerPrecision(
        this.lastExpectedTriggerTime, 
        actualTriggerTime
      );
      this.updateStats(precision);
    }

    // 如果页面不可见，跳过更新但继续调度
    if (!this.isPageVisible) {
      this.stats.skippedTriggers++;
      this.log('Page not visible, skipping update');
      this.scheduleNextUpdate();
      return;
    }

    // 执行时间更新
    this.options.onTimeUpdate();
    this.stats.lastTriggerTime = actualTriggerTime;
    
    this.log('Time update triggered');
    
    // 调度下一次更新
    this.scheduleNextUpdate();
  }

  /**
   * 更新统计信息
   */
  private updateStats(precision: ReturnType<typeof validateTriggerPrecision>): void {
    this.stats.totalTriggers++;
    
    if (precision.isAccurate) {
      this.stats.accurateTriggers++;
    }
    
    // 更新平均偏差
    const totalDeviation = this.stats.averageDeviation * (this.stats.totalTriggers - 1) + precision.deviation;
    this.stats.averageDeviation = totalDeviation / this.stats.totalTriggers;
    
    // 更新最大偏差
    this.stats.maxDeviation = Math.max(this.stats.maxDeviation, precision.deviation);
    
    this.log(`Trigger precision: ${precision.deviation.toFixed(2)}ms deviation`);
  }

  /**
   * 设置页面可见性监听器
   */
  private setupVisibilityListener(): void {
    if (typeof document === 'undefined') {
      return;
    }

    const handleVisibilityChange = () => {
      const wasVisible = this.isPageVisible;
      this.isPageVisible = !document.hidden;
      
      this.log(`Page visibility changed: ${this.isPageVisible ? 'visible' : 'hidden'}`);
      
      // 页面从隐藏变为可见时，重新同步
      if (!wasVisible && this.isPageVisible && this.options.syncOnVisibilityChange) {
        this.log('Page became visible, re-syncing...');
        this.resync();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
  }

  /**
   * 启动错误恢复检查
   */
  private startRecoveryCheck(): void {
    this.recoveryIntervalId = window.setInterval(() => {
      if (!this.isRunning) {
        return;
      }

      // 检查是否需要重新同步
      const now = getHighPrecisionTimestamp();
      const timeSinceLastTrigger = now - this.stats.lastTriggerTime;
      
      // 如果超过预期时间很久没有触发，重新同步
      const maxExpectedInterval = this.options.useTimeSlotBoundary ? 35 * 60 * 1000 : 70 * 1000; // 35分钟或70秒
      
      if (timeSinceLastTrigger > maxExpectedInterval) {
        this.log('Recovery check: re-syncing due to missed triggers');
        this.resync();
      }
    }, this.options.recoveryCheckInterval);
  }

  /**
   * 重新同步时间触发器
   */
  private resync(): void {
    if (!this.isRunning) {
      return;
    }

    this.log('Re-syncing time updater');
    
    // 清除当前的调度
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    
    // 立即执行更新
    this.options.onTimeUpdate();
    
    // 重新调度
    this.scheduleNextUpdate();
  }

  /**
   * 调试日志输出
   */
  private log(message: string): void {
    if (this.options.debug) {
      console.log(`[SmartTimeUpdater] ${message}`);
    }
  }

  /**
   * 销毁时间更新器
   */
  destroy(): void {
    this.stop();
    // 移除事件监听器等清理工作在这里进行
  }
}
