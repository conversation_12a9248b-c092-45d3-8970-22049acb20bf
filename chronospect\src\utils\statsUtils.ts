import { TimeBlock, Activity, ActivityStats, TimeRange, DailyStats } from '@/types';
import { formatTime, formatDuration } from './timeUtils';

/**
 * 计算指定日期的活动统计数据
 * @param timeBlocks 时间块数组
 * @param activities 活动数组
 * @returns 每日统计数据
 */
export function calculateDailyStats(
  timeBlocks: TimeBlock[], 
  activities: Activity[]
): DailyStats {
  // 过滤出已填充的时间块
  const filledBlocks = timeBlocks.filter(block => block.activityId !== null);
  const totalFilledMinutes = filledBlocks.length * 30;
  const totalMinutes = 24 * 60; // 一天总分钟数
  
  // 按活动分组统计
  const activityStatsMap = new Map<string, {
    blocks: TimeBlock[];
    activity: Activity;
  }>();

  // 收集每个活动的时间块
  filledBlocks.forEach(block => {
    const activity = activities.find(a => a.id === block.activityId);
    if (activity) {
      if (!activityStatsMap.has(activity.id)) {
        activityStatsMap.set(activity.id, {
          blocks: [],
          activity
        });
      }
      activityStatsMap.get(activity.id)!.blocks.push(block);
    }
  });

  // 生成活动统计数据
  const activityStats: ActivityStats[] = Array.from(activityStatsMap.entries()).map(
    ([activityId, { blocks, activity }]) => {
      const totalBlocks = blocks.length;
      const totalMinutes = totalBlocks * 30;
      const percentage = totalFilledMinutes > 0 ? (totalMinutes / totalFilledMinutes) * 100 : 0;
      
      // 生成时间段列表
      const timeRanges = generateTimeRanges(blocks);

      return {
        activityId,
        activityName: activity.name,
        color: activity.color,
        totalBlocks,
        totalMinutes,
        percentage,
        timeRanges
      };
    }
  );

  // 按时间长度排序
  activityStats.sort((a, b) => b.totalMinutes - a.totalMinutes);

  return {
    date: timeBlocks[0]?.date || '',
    totalMinutes,
    filledMinutes: totalFilledMinutes,
    unfilledMinutes: totalMinutes - totalFilledMinutes,
    filledPercentage: (totalFilledMinutes / totalMinutes) * 100,
    activities: activityStats
  };
}

/**
 * 将时间块转换为连续的时间段
 * @param blocks 时间块数组
 * @returns 时间段数组
 */
export function generateTimeRanges(blocks: TimeBlock[]): TimeRange[] {
  if (blocks.length === 0) return [];

  // 按时间槽排序
  const sortedBlocks = [...blocks].sort((a, b) => a.timeSlot - b.timeSlot);
  const ranges: TimeRange[] = [];
  
  interface CurrentRange {
    startSlot: number;
    endSlot: number;
    startTime: string;
    endTime: string;
  }

  let currentRange: CurrentRange | null = null;

  sortedBlocks.forEach(block => {
    if (currentRange === null) {
      // 开始新的时间段
      currentRange = {
        startSlot: block.timeSlot,
        endSlot: block.timeSlot,
        startTime: block.startTime,
        endTime: block.endTime
      };
    } else if (block.timeSlot === currentRange.endSlot + 1) {
      // 连续的时间块，扩展当前时间段
      currentRange.endSlot = block.timeSlot;
      currentRange.endTime = block.endTime;
    } else {
      // 不连续，保存当前时间段并开始新的
      const range = currentRange as CurrentRange;
      ranges.push({
        startTime: range.startTime,
        endTime: range.endTime,
        duration: (range.endSlot - range.startSlot + 1) * 30
      });
      
      currentRange = {
        startSlot: block.timeSlot,
        endSlot: block.timeSlot,
        startTime: block.startTime,
        endTime: block.endTime
      };
    }
  });

  // 添加最后一个时间段
  if (currentRange !== null) {
    const range = currentRange as CurrentRange;
    ranges.push({
      startTime: range.startTime,
      endTime: range.endTime,
      duration: (range.endSlot - range.startSlot + 1) * 30
    });
  }

  return ranges;
}

/**
 * 获取活动的总时长描述
 * @param stats 活动统计数据
 * @returns 格式化的时长字符串
 */
export function getActivityDurationText(stats: ActivityStats): string {
  return formatDuration(stats.totalMinutes);
}

/**
 * 获取活动的时间段描述
 * @param timeRanges 时间段数组
 * @returns 时间段描述字符串
 */
export function getTimeRangesText(timeRanges: TimeRange[]): string {
  if (timeRanges.length === 0) return '';
  
  if (timeRanges.length === 1) {
    const range = timeRanges[0];
    return `${range.startTime} - ${range.endTime}`;
  }
  
  return `${timeRanges.length}个时间段`;
}

/**
 * 计算活动的效率分数（基于连续性）
 * @param timeRanges 时间段数组
 * @param totalMinutes 总分钟数
 * @returns 效率分数 (0-100)
 */
export function calculateEfficiencyScore(timeRanges: TimeRange[], totalMinutes: number): number {
  if (timeRanges.length === 0 || totalMinutes === 0) return 0;
  
  // 连续性越高，效率分数越高
  const averageDuration = totalMinutes / timeRanges.length;
  const maxPossibleDuration = totalMinutes; // 如果全部连续
  
  return Math.min(100, (averageDuration / maxPossibleDuration) * 100);
}

/**
 * 获取一天中最活跃的时间段
 * @param dailyStats 每日统计数据
 * @returns 最活跃的活动统计
 */
export function getMostActiveActivity(dailyStats: DailyStats): ActivityStats | null {
  if (dailyStats.activities.length === 0) return null;
  
  return dailyStats.activities.reduce((prev, current) => 
    current.totalMinutes > prev.totalMinutes ? current : prev
  );
}

/**
 * 获取时间利用率等级
 * @param filledPercentage 填充百分比
 * @returns 等级描述
 */
export function getUtilizationLevel(filledPercentage: number): {
  level: string;
  color: string;
  description: string;
} {
  if (filledPercentage >= 80) {
    return {
      level: '高效',
      color: '#10B981', // green-500
      description: '时间利用率很高'
    };
  } else if (filledPercentage >= 60) {
    return {
      level: '良好',
      color: '#3B82F6', // blue-500
      description: '时间利用率不错'
    };
  } else if (filledPercentage >= 40) {
    return {
      level: '一般',
      color: '#F59E0B', // amber-500
      description: '还有提升空间'
    };
  } else {
    return {
      level: '较低',
      color: '#EF4444', // red-500
      description: '建议记录更多时间'
    };
  }
}

/**
 * 生成每日总结文本
 * @param dailyStats 每日统计数据
 * @returns 总结文本
 */
export function generateDailySummary(dailyStats: DailyStats): string {
  const { filledMinutes, activities } = dailyStats;
  const filledHours = Math.floor(filledMinutes / 60);
  const remainingMinutes = filledMinutes % 60;
  
  if (activities.length === 0) {
    return '今天还没有记录任何活动';
  }
  
  const mostActive = getMostActiveActivity(dailyStats);
  const timeText = filledHours > 0 
    ? `${filledHours}小时${remainingMinutes > 0 ? remainingMinutes + '分钟' : ''}`
    : `${remainingMinutes}分钟`;
  
  return `今天记录了${timeText}，主要活动是${mostActive?.activityName}`;
}
