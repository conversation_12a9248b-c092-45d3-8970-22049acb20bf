Date : 2025-07-28 21:42:13
Directory : d:\git_resp\Unfinished\time_blocks
Total : 73 files,  16251 codes, 1116 comments, 1731 blanks, all 19098 lines

Languages
+--------------------+------------+------------+------------+------------+------------+
| language           | files      | code       | comment    | blank      | total      |
+--------------------+------------+------------+------------+------------+------------+
| JSON               |          2 |      5,930 |          0 |          2 |      5,932 |
| TypeScript JSX     |         27 |      5,216 |        381 |        539 |      6,136 |
| TypeScript         |         13 |      2,532 |        705 |        502 |      3,739 |
| Markdown           |         21 |      2,219 |          0 |        619 |      2,838 |
| PostCSS            |          2 |        306 |         30 |         61 |        397 |
| JSON with Comments |          1 |         27 |          0 |          1 |         28 |
| JavaScript         |          2 |         16 |          0 |          7 |         23 |
| XML                |          5 |          5 |          0 |          0 |          5 |
+--------------------+------------+------------+------------+------------+------------+

Directories
+-------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                      | files      | code       | comment    | blank      | total      |
+-------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                         |         73 |     16,251 |      1,116 |      1,731 |     19,098 |
| . (Files)                                                                                 |          2 |        224 |          0 |         64 |        288 |
| chronospect                                                                               |         53 |     14,170 |      1,116 |      1,152 |     16,438 |
| chronospect (Files)                                                                       |          7 |      6,115 |          1 |         53 |      6,169 |
| chronospect\public                                                                        |          5 |          5 |          0 |          0 |          5 |
| chronospect\src                                                                           |         41 |      8,050 |      1,115 |      1,099 |     10,264 |
| chronospect\src\app                                                                       |          3 |        367 |         40 |         61 |        468 |
| chronospect\src\components                                                                |         26 |      5,155 |        371 |        539 |      6,065 |
| chronospect\src\hooks                                                                     |          1 |         19 |         13 |          8 |         40 |
| chronospect\src\stores                                                                    |          1 |        653 |         76 |        111 |        840 |
| chronospect\src\types                                                                     |          1 |        126 |          9 |         11 |        146 |
| chronospect\src\utils                                                                     |          9 |      1,730 |        606 |        369 |      2,705 |
| docs                                                                                      |          3 |        374 |          0 |        112 |        486 |
| issues                                                                                    |         15 |      1,483 |          0 |        403 |      1,886 |
+-------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+-------------------------------------------------------------------------------------------+--------------------+------------+------------+------------+------------+
| filename                                                                                  | language           | code       | comment    | blank      | total      |
+-------------------------------------------------------------------------------------------+--------------------+------------+------------+------------+------------+
| d:\git_resp\Unfinished\time_blocks\README.md                                              | Markdown           |         80 |          0 |         29 |        109 |
| d:\git_resp\Unfinished\time_blocks\chronospect\README.md                                  | Markdown           |        138 |          0 |         40 |        178 |
| d:\git_resp\Unfinished\time_blocks\chronospect\eslint.config.mjs                          | JavaScript         |         12 |          0 |          5 |         17 |
| d:\git_resp\Unfinished\time_blocks\chronospect\next.config.ts                             | TypeScript         |          4 |          1 |          3 |          8 |
| d:\git_resp\Unfinished\time_blocks\chronospect\package-lock.json                          | JSON               |      5,897 |          0 |          1 |      5,898 |
| d:\git_resp\Unfinished\time_blocks\chronospect\package.json                               | JSON               |         33 |          0 |          1 |         34 |
| d:\git_resp\Unfinished\time_blocks\chronospect\postcss.config.mjs                         | JavaScript         |          4 |          0 |          2 |          6 |
| d:\git_resp\Unfinished\time_blocks\chronospect\public\file.svg                            | XML                |          1 |          0 |          0 |          1 |
| d:\git_resp\Unfinished\time_blocks\chronospect\public\globe.svg                           | XML                |          1 |          0 |          0 |          1 |
| d:\git_resp\Unfinished\time_blocks\chronospect\public\next.svg                            | XML                |          1 |          0 |          0 |          1 |
| d:\git_resp\Unfinished\time_blocks\chronospect\public\vercel.svg                          | XML                |          1 |          0 |          0 |          1 |
| d:\git_resp\Unfinished\time_blocks\chronospect\public\window.svg                          | XML                |          1 |          0 |          0 |          1 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\app\globals.css                        | PostCSS            |        225 |         28 |         44 |        297 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\app\layout.tsx                         | TypeScript JSX     |         30 |          0 |          5 |         35 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\app\page.tsx                           | TypeScript JSX     |        112 |         12 |         12 |        136 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityChart.tsx           | TypeScript JSX     |        232 |          7 |         11 |        250 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityForm.tsx            | TypeScript JSX     |        298 |         24 |         39 |        361 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityList.tsx            | TypeScript JSX     |        173 |         12 |         17 |        202 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityManagementModal.tsx | TypeScript JSX     |        317 |         20 |         36 |        373 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ActivityPalette.tsx         | TypeScript JSX     |         92 |         13 |         18 |        123 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ColorPicker.tsx             | TypeScript JSX     |        171 |         23 |         25 |        219 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\CustomDatePicker.module.css | PostCSS            |         81 |          2 |         17 |        100 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\CustomDatePicker.tsx        | TypeScript JSX     |        174 |         12 |         17 |        203 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DailyStatsCard.tsx          | TypeScript JSX     |        189 |          8 |         12 |        209 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DataManagementDropdown.tsx  | TypeScript JSX     |        164 |         12 |         21 |        197 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DateComparison.tsx          | TypeScript JSX     |        367 |         17 |         25 |        409 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DateNavigator.tsx           | TypeScript JSX     |        140 |          8 |         12 |        160 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DeleteConfirmDialog.tsx     | TypeScript JSX     |        158 |          9 |         15 |        182 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DownloadTestPanel.tsx       | TypeScript JSX     |        209 |          4 |         23 |        236 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DragGuide.tsx               | TypeScript JSX     |        266 |         12 |         21 |        299 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\DragPreview.tsx             | TypeScript JSX     |        218 |         17 |         22 |        257 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\HydrationBoundary.tsx       | TypeScript JSX     |         16 |          7 |          7 |         30 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\IconSelector.tsx            | TypeScript JSX     |        197 |         11 |         20 |        228 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ImportModal.tsx             | TypeScript JSX     |        244 |         14 |         22 |        280 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\LoadingSkeleton.tsx         | TypeScript JSX     |        199 |         33 |         19 |        251 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\PortalModal.tsx             | TypeScript JSX     |        105 |          9 |         17 |        131 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\ReviewView.tsx              | TypeScript JSX     |        228 |         14 |         20 |        262 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimeBlock.tsx               | TypeScript JSX     |        266 |         13 |         18 |        297 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimeGrid.tsx                | TypeScript JSX     |        382 |         40 |         45 |        467 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimeUpdaterDebugPanel.tsx   | TypeScript JSX     |        137 |         12 |         19 |        168 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\components\TimelineIndicator.tsx       | TypeScript JSX     |        132 |         18 |         21 |        171 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\hooks\useIsClient.ts                   | TypeScript         |         19 |         13 |          8 |         40 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\stores\useAppStore.ts                  | TypeScript         |        653 |         76 |        111 |        840 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\types\index.ts                         | TypeScript         |        126 |          9 |         11 |        146 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\activityImpactAnalysis.ts        | TypeScript         |        134 |         52 |         27 |        213 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\cacheUtils.ts                    | TypeScript         |        163 |         46 |         33 |        242 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\dataExport.ts                    | TypeScript         |        276 |         50 |         50 |        376 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\dataImport.ts                    | TypeScript         |        239 |         39 |         44 |        322 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\dragUtils.ts                     | TypeScript         |        162 |        106 |         38 |        306 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\loadingUtils.ts                  | TypeScript         |        178 |         47 |         33 |        258 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\smartTimeUpdater.ts              | TypeScript         |        179 |         82 |         51 |        312 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\statsUtils.ts                    | TypeScript         |        163 |         54 |         30 |        247 |
| d:\git_resp\Unfinished\time_blocks\chronospect\src\utils\timeUtils.ts                     | TypeScript         |        236 |        130 |         63 |        429 |
| d:\git_resp\Unfinished\time_blocks\chronospect\tsconfig.json                              | JSON with Comments |         27 |          0 |          1 |         28 |
| d:\git_resp\Unfinished\time_blocks\docs\坐标系统说明.md                                         | Markdown           |         99 |          0 |         23 |        122 |
| d:\git_resp\Unfinished\time_blocks\docs\数据导出功能说明.md                                       | Markdown           |        146 |          0 |         41 |        187 |
| d:\git_resp\Unfinished\time_blocks\docs\智能时间触发器使用说明.md                                    | Markdown           |        129 |          0 |         48 |        177 |
| d:\git_resp\Unfinished\time_blocks\issues\ActivityPalette改造_2025-07-18.md                 | Markdown           |         44 |          0 |          9 |         53 |
| d:\git_resp\Unfinished\time_blocks\issues\Chronospect开发任务_2025-07-17.md                   | Markdown           |        133 |          0 |         33 |        166 |
| d:\git_resp\Unfinished\time_blocks\issues\Chronospect美学优化与测试_2025-07-19.md                | Markdown           |        152 |          0 |         35 |        187 |
| d:\git_resp\Unfinished\time_blocks\issues\Portal模态框导入流程优化_2025-07-28.md                   | Markdown           |         84 |          0 |         30 |        114 |
| d:\git_resp\Unfinished\time_blocks\issues\SSR_Hydration_修复_2025-07-20.md                  | Markdown           |         35 |          0 |         13 |         48 |
| d:\git_resp\Unfinished\time_blocks\issues\列优先垂直拖拽算法实现_2025-07-21.md                       | Markdown           |         91 |          0 |         29 |        120 |
| d:\git_resp\Unfinished\time_blocks\issues\复盘功能开发_2025-07-18.md                            | Markdown           |        104 |          0 |         27 |        131 |
| d:\git_resp\Unfinished\time_blocks\issues\数据导入导出功能开发_2025-07-28.md                        | Markdown           |        143 |          0 |         42 |        185 |
| d:\git_resp\Unfinished\time_blocks\issues\时间管理系统界面优化_2025-07-24.md                        | Markdown           |         54 |          0 |         17 |         71 |
| d:\git_resp\Unfinished\time_blocks\issues\时间轴流式布局改造_2025-07-20.md                         | Markdown           |         62 |          0 |         10 |         72 |
| d:\git_resp\Unfinished\time_blocks\issues\时间验证功能修复_2025-07-20.md                          | Markdown           |         93 |          0 |         30 |        123 |
| d:\git_resp\Unfinished\time_blocks\issues\智能精确时间触发器_2025-07-20.md                         | Markdown           |         91 |          0 |         27 |        118 |
| d:\git_resp\Unfinished\time_blocks\issues\活动管理CRUD功能开发_2025-07-21.md                      | Markdown           |        202 |          0 |         50 |        252 |
| d:\git_resp\Unfinished\time_blocks\issues\活动管理界面优化_2025-07-22.md                          | Markdown           |        139 |          0 |         40 |        179 |
| d:\git_resp\Unfinished\time_blocks\issues\跨列选择坐标系统修复_2025-07-21.md                        | Markdown           |         56 |          0 |         11 |         67 |
| d:\git_resp\Unfinished\time_blocks\产品需求文档 (PRD)_ Chronospect (时间洞察) v1.1.md               | Markdown           |        144 |          0 |         35 |        179 |
| Total                                                                                     |                    |     16,251 |      1,116 |      1,731 |     19,098 |
+-------------------------------------------------------------------------------------------+--------------------+------------+------------+------------+------------+