'use client';

import React, { useState, useEffect } from 'react';
import { DayPicker, getDefaultClassNames } from 'react-day-picker';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, X } from 'lucide-react';
import 'react-day-picker/style.css';
import styles from './CustomDatePicker.module.css';
import { useIsClient } from '@/hooks/useIsClient';

interface CustomDatePickerProps {
  value: string;
  onChange: (date: string) => void;
  className?: string;
}

export function CustomDatePicker({ value, onChange, className = '' }: CustomDatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    value ? new Date(value) : undefined
  );
  const isClient = useIsClient();

  const defaultClassNames = getDefaultClassNames();

  // 同步外部value变化到内部状态
  useEffect(() => {
    if (value) {
      setSelectedDate(new Date(value));
    } else {
      setSelectedDate(undefined);
    }
  }, [value]);

  // 格式化显示日期 - 添加SSR安全检查
  const formatDisplayDate = (date: Date) => {
    // 在SSR期间返回安全的默认值
    if (!isClient) {
      return '1970年1月1日星期四';
    }

    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  };

  // 处理日期选择
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      const formattedDate = date.toISOString().split('T')[0];
      onChange(formattedDate);
      setIsOpen(false);
    }
  };

  // 处理输入框点击
  const handleInputClick = () => {
    setIsOpen(!isOpen);
  };

  // 处理关闭
  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`}>
      {/* 日期输入框 */}
      <motion.div
        className="card flex items-center cursor-pointer"
        style={{
          gap: 'var(--spacing-2)',
          padding: 'var(--spacing-4)',
          background: 'white',
          borderRadius: 'var(--radius-xl)',
          boxShadow: 'var(--shadow-md)',
          border: '1px solid var(--neutral-200)',
          minWidth: '200px',
          justifyContent: 'center'
        }}
        onClick={handleInputClick}
        whileHover={{ 
          scale: 1.02,
          boxShadow: 'var(--shadow-lg)',
          borderColor: 'var(--neutral-300)'
        }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.15 }}
      >
        <Calendar size={18} style={{ color: 'var(--neutral-500)' }} />
        <span style={{ 
          fontWeight: 'var(--font-weight-medium)', 
          color: 'var(--neutral-900)',
          fontSize: 'var(--font-size-base)'
        }}>
          {selectedDate ? formatDisplayDate(selectedDate) : '选择日期'}
        </span>
      </motion.div>

      {/* 日期选择器弹窗 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 遮罩层 */}
            <motion.div
              className="fixed inset-0 z-40"
              style={{ background: 'rgba(0, 0, 0, 0.1)' }}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={handleClose}
            />

            {/* 日期选择器容器 */}
            <motion.div
              className="absolute z-50"
              style={{
                top: '100%',
                left: '50%',
                marginTop: 'var(--spacing-2)',
                background: 'white',
                borderRadius: 'var(--radius-xl)',
                boxShadow: 'var(--shadow-2xl)',
                border: '1px solid var(--neutral-200)',
                padding: 'var(--spacing-6)',
                minWidth: '320px'
              }}
              initial={{ 
                opacity: 0, 
                scale: 0.9, 
                y: -10,
                x: '-50%'
              }}
              animate={{ 
                opacity: 1, 
                scale: 1, 
                y: 0,
                x: '-50%'
              }}
              exit={{ 
                opacity: 0, 
                scale: 0.9, 
                y: -10,
                x: '-50%'
              }}
              transition={{ 
                duration: 0.2, 
                ease: [0.4, 0, 0.2, 1] 
              }}
            >
              {/* 关闭按钮 */}
              <motion.button
                onClick={handleClose}
                className="absolute top-3 right-3 p-1"
                style={{
                  background: 'var(--neutral-100)',
                  borderRadius: 'var(--radius-full)',
                  border: 'none',
                  cursor: 'pointer'
                }}
                whileHover={{ 
                  background: 'var(--neutral-200)',
                  scale: 1.1
                }}
                whileTap={{ scale: 0.9 }}
              >
                <X size={16} style={{ color: 'var(--neutral-600)' }} />
              </motion.button>

              {/* DayPicker组件 */}
              <div className={styles.customDayPicker}>
                <DayPicker
                  mode="single"
                  selected={selectedDate}
                  onSelect={handleDateSelect}
                  showOutsideDays
                  className={defaultClassNames.root}
                  classNames={{
                    ...defaultClassNames,
                    today: `${defaultClassNames.today} ${styles.rdpTodayCustom}`,
                    selected: `${defaultClassNames.selected} ${styles.rdpSelectedCustom}`,
                    root: `${defaultClassNames.root} ${styles.rdpRootCustom}`,
                    chevron: `${defaultClassNames.chevron} ${styles.rdpChevronCustom}`,
                    month_caption: `${defaultClassNames.month_caption} ${styles.rdpCaptionCustom}`,
                    weekdays: `${defaultClassNames.weekdays} ${styles.rdpWeekdaysCustom}`,
                    day_button: `${defaultClassNames.day_button} ${styles.rdpDayButtonCustom}`
                  }}
                />
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>


    </div>
  );
}
