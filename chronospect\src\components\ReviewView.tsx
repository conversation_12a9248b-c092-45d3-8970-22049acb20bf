'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';
import { DailyStatsCard } from './DailyStatsCard';
import { ActivityChart } from './ActivityChart';
import { ActivityList } from './ActivityList';
import { DateComparison } from './DateComparison';
import { formatDate } from '@/utils/timeUtils';
import { calculateDailyStats } from '@/utils/statsUtils';
import { DailyStats } from '@/types';
import {
  ReviewViewSkeleton,
  LoadingIndicator,
  LoadingError
} from './LoadingSkeleton';

export function ReviewView() {
  const {
    currentDate,
    getDailyStatsSync,
    getDailyStatsAsync,
    getTimeBlocksForDate,
    activities,
    isViewPreloaded
  } = useAppStore();

  // 检查数据是否已预加载
  const viewKey = `review-${currentDate}`;
  const isDataPreloaded = isViewPreloaded(viewKey);

  // 优化的初始状态获取 - 优先使用预加载的数据
  const initialStats = useMemo(() => {
    // 尝试从缓存获取
    const cached = getDailyStatsSync(currentDate);
    if (cached) return cached;

    // 如果没有缓存，进行本地计算
    const timeBlocks = getTimeBlocksForDate(currentDate);
    return calculateDailyStats(timeBlocks, activities);
  }, [currentDate, getDailyStatsSync, getTimeBlocksForDate, activities]);

  const [dailyStats, setDailyStats] = useState<DailyStats>(initialStats);
  const [isLoading, setIsLoading] = useState(!isDataPreloaded);
  const [error, setError] = useState<string | null>(null);

  // 监听日期变化，智能加载数据
  useEffect(() => {
    const loadData = async () => {
      // 如果数据已预加载，直接使用缓存数据
      if (isDataPreloaded) {
        const cached = getDailyStatsSync(currentDate);
        if (cached) {
          setDailyStats(cached);
          setIsLoading(false);
          return;
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        const stats = await getDailyStatsAsync(currentDate);
        setDailyStats(stats);
      } catch (err) {
        setError(err instanceof Error ? err.message : '数据加载失败');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [currentDate, getDailyStatsAsync, isDataPreloaded, getDailyStatsSync]);

  // 重试函数 - 使用安全的数据获取方式
  const handleRetry = () => {
    // 尝试从缓存获取
    const cached = getDailyStatsSync(currentDate);
    if (cached) {
      setDailyStats(cached);
    } else {
      // 如果没有缓存，进行本地计算
      const timeBlocks = getTimeBlocksForDate(currentDate);
      const stats = calculateDailyStats(timeBlocks, activities);
      setDailyStats(stats);
    }
    setError(null);
  };

  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return <ReviewViewSkeleton />;
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="w-full max-w-6xl mx-auto px-4 py-6">
        <LoadingError message={error} onRetry={handleRetry} />
      </div>
    );
  }

  return (
    <motion.div
      className="w-full max-w-6xl mx-auto"
      style={{
        padding: 'var(--spacing-4) var(--spacing-6)'
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
    >
      {/* 页面标题 */}
      <motion.div
        style={{ marginBottom: 'var(--spacing-8)' }}
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.3 }}
      >
        <h1 style={{
          fontSize: 'var(--font-size-3xl)',
          fontWeight: 'var(--font-weight-bold)',
          color: 'var(--neutral-900)',
          marginBottom: 'var(--spacing-2)',
          lineHeight: 'var(--line-height-tight)'
        }}>
          📈 每日复盘
        </h1>
        <p style={{
          fontSize: 'var(--font-size-lg)',
          color: 'var(--neutral-600)',
          lineHeight: 'var(--line-height-normal)'
        }}>
          {formatDate(currentDate)} 的时间分析
        </p>
      </motion.div>

      {/* 统计概览卡片 */}
      <motion.div
        style={{ marginBottom: 'var(--spacing-8)' }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.3 }}
      >
        <DailyStatsCard stats={dailyStats} />
      </motion.div>

      {/* 日期对比分析 */}
      <motion.div
        style={{ marginBottom: 'var(--spacing-8)' }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <DateComparison currentDate={currentDate} />
      </motion.div>

      {/* 主要内容区域 */}
      <motion.div
        className="grid grid-cols-1 lg:grid-cols-2"
        style={{ gap: 'var(--spacing-8)' }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.3 }}
      >
        {/* 左侧：数据可视化 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-6)' }}>
          <ActivityChart stats={dailyStats} />
        </div>

        {/* 右侧：活动列表 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-6)' }}>
          <ActivityList stats={dailyStats} />
        </div>
      </motion.div>

      {/* 底部洞察区域 */}
      {dailyStats.activities.length > 0 && (
        <motion.div
          className="card"
          style={{
            marginTop: 'var(--spacing-12)',
            padding: 'var(--spacing-6)',
            background: 'linear-gradient(135deg, var(--primary-50), var(--info-50))',
            borderRadius: 'var(--radius-xl)',
            border: '1px solid var(--primary-100)',
            boxShadow: 'var(--shadow-lg)'
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
          whileHover={{
            boxShadow: 'var(--shadow-xl)',
            borderColor: 'var(--primary-200)'
          }}
        >
          <h3 style={{
            fontSize: 'var(--font-size-lg)',
            fontWeight: 'var(--font-weight-semibold)',
            color: 'var(--neutral-900)',
            marginBottom: 'var(--spacing-3)'
          }}>
            💡 今日洞察
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2" style={{ gap: 'var(--spacing-4)' }}>
            <InsightCard
              title="最长活动"
              value={dailyStats.activities[0]?.activityName || '无'}
              description={`持续 ${Math.floor(dailyStats.activities[0]?.totalMinutes / 60 || 0)} 小时 ${(dailyStats.activities[0]?.totalMinutes % 60) || 0} 分钟`}
              icon="🎯"
            />
            <InsightCard
              title="时间利用率"
              value={`${dailyStats.filledPercentage.toFixed(1)}%`}
              description={getUtilizationDescription(dailyStats.filledPercentage)}
              icon="📊"
            />
          </div>
        </motion.div>
      )}

      {/* 空状态 */}
      {dailyStats.activities.length === 0 && (
        <motion.div
          className="text-center"
          style={{ padding: 'var(--spacing-16) 0' }}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.2, duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
        >
          <motion.div
            style={{
              fontSize: 'var(--font-size-6xl)',
              marginBottom: 'var(--spacing-4)'
            }}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.4, duration: 0.3, ease: [0.68, -0.55, 0.265, 1.55] }}
          >
            📝
          </motion.div>
          <h3 style={{
            fontSize: 'var(--font-size-xl)',
            fontWeight: 'var(--font-weight-semibold)',
            color: 'var(--neutral-900)',
            marginBottom: 'var(--spacing-2)'
          }}>
            还没有记录任何活动
          </h3>
          <p style={{
            color: 'var(--neutral-600)',
            marginBottom: 'var(--spacing-6)',
            fontSize: 'var(--font-size-base)'
          }}>
            回到网格视图开始记录您的时间吧
          </p>
        </motion.div>
      )}
    </motion.div>
  );
}

// 洞察卡片组件
interface InsightCardProps {
  title: string;
  value: string;
  description: string;
  icon: string;
}

function InsightCard({ title, value, description, icon }: InsightCardProps) {
  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200">
      <div className="flex items-center mb-2">
        <span className="text-2xl mr-2">{icon}</span>
        <h4 className="font-medium text-gray-900">{title}</h4>
      </div>
      <div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
      <div className="text-sm text-gray-600">{description}</div>
    </div>
  );
}

// 获取利用率描述
function getUtilizationDescription(percentage: number): string {
  if (percentage >= 80) {
    return '时间利用率很高，保持下去！';
  } else if (percentage >= 60) {
    return '时间利用率不错，还有提升空间';
  } else if (percentage >= 40) {
    return '建议记录更多的时间活动';
  } else {
    return '开始记录您的时间活动吧';
  }
}
