'use client';

import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, Calendar, BarChart3, Target, Lightbulb } from 'lucide-react';
import type { DragPath } from '@/utils/dragUtils';

interface SelectionInfoPanelProps {
  selectedBlocks: number[];
  dragPath: DragPath | null;
  className?: string;
}

interface SelectionInfo {
  timeRange: string;
  duration: string;
  segmentCount: number;
  blockCount: number;
  suggestion: string;
  dragType: string;
}

// 计算选择信息的工具函数
function calculateSelectionInfo(selectedBlocks: number[], dragPath: DragPath | null): SelectionInfo | null {
  if (selectedBlocks.length === 0) return null;

  // 计算时间范围
  const sortedBlocks = [...selectedBlocks].sort((a, b) => a - b);
  const startBlock = sortedBlocks[0];
  const endBlock = sortedBlocks[sortedBlocks.length - 1];
  
  const startHour = Math.floor(startBlock / 2);
  const startMinute = (startBlock % 2) * 30;
  const endHour = Math.floor((endBlock + 1) / 2);
  const endMinute = ((endBlock + 1) % 2) * 30;
  
  const timeRange = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
  
  // 计算总时长
  const totalMinutes = selectedBlocks.length * 30;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  const duration = hours > 0 
    ? (minutes > 0 ? `${hours}小时${minutes}分钟` : `${hours}小时`)
    : `${minutes}分钟`;
  
  // 计算跨越的时间段数量
  const segments = new Set(selectedBlocks.map(block => Math.floor(block / 8)));
  const segmentCount = segments.size;
  
  // 获取拖拽类型和建议
  const dragType = dragPath?.direction.type === 'vertical' ? '垂直选择' : '跨列选择';
  const suggestion = getSuggestion(totalMinutes, segmentCount, dragPath?.direction.type);
  
  return {
    timeRange,
    duration,
    segmentCount,
    blockCount: selectedBlocks.length,
    suggestion,
    dragType
  };
}

// 根据选择情况提供建议
function getSuggestion(totalMinutes: number, segmentCount: number, dragType?: string): string {
  if (totalMinutes >= 120) {
    return '适合深度工作';
  } else if (totalMinutes >= 60) {
    return '适合专注任务';
  } else if (segmentCount > 2) {
    return '时间较分散';
  } else if (dragType === 'vertical') {
    return '时间连续性好';
  } else {
    return '跨时段选择';
  }
}

// 信息行组件
interface InfoRowProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  priority: 'high' | 'medium' | 'low';
}

const InfoRow: React.FC<InfoRowProps> = ({ icon, label, value, priority }) => {
  const styles = {
    high: {
      textClass: 'text-base font-semibold',
      textColor: 'var(--neutral-900)',
      iconColor: 'var(--primary-600)'
    },
    medium: {
      textClass: 'text-sm font-medium',
      textColor: 'var(--neutral-700)',
      iconColor: 'var(--neutral-600)'
    },
    low: {
      textClass: 'text-xs font-normal',
      textColor: 'var(--neutral-500)',
      iconColor: 'var(--neutral-400)'
    }
  }[priority];

  return (
    <motion.div
      className="flex items-center gap-3 py-1.5"
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2, delay: priority === 'high' ? 0 : priority === 'medium' ? 0.05 : 0.1 }}
    >
      <div
        className="flex-shrink-0 w-4 h-4"
        style={{ color: styles.iconColor }}
      >
        {icon}
      </div>
      <div className="flex-1 min-w-0">
        <span
          className={styles.textClass}
          style={{ color: styles.textColor }}
        >
          {value}
        </span>
      </div>
    </motion.div>
  );
};

// 分割线组件
const Divider: React.FC = () => (
  <motion.div
    className="my-3"
    initial={{ scaleX: 0 }}
    animate={{ scaleX: 1 }}
    transition={{ duration: 0.3, delay: 0.1 }}
    style={{
      height: '1px',
      background: 'var(--neutral-200)',
      transformOrigin: 'left'
    }}
  />
);

export const SelectionInfoPanel: React.FC<SelectionInfoPanelProps> = ({
  selectedBlocks,
  dragPath,
  className = ''
}) => {
  const selectionInfo = useMemo(() => 
    calculateSelectionInfo(selectedBlocks, dragPath), 
    [selectedBlocks, dragPath]
  );

  return (
    <AnimatePresence>
      {selectionInfo && (
        <motion.div
          className={`
            fixed right-6 top-1/2 -translate-y-1/2
            w-60 z-50
            ${className}
          `}
          style={{
            background: 'var(--background)',
            borderRadius: 'var(--radius-lg)',
            boxShadow: 'var(--shadow-lg)',
            border: '1px solid var(--neutral-200)',
            padding: 'var(--spacing-4)',
            backdropFilter: 'blur(8px)',
            backgroundColor: 'rgba(255, 255, 255, 0.95)'
          }}
          initial={{ opacity: 0, scale: 0.95, x: 20 }}
          animate={{ opacity: 1, scale: 1, x: 0 }}
          exit={{ opacity: 0, scale: 0.95, x: 20 }}
          transition={{
            duration: 0.25,
            ease: [0.4, 0, 0.2, 1]
          }}
        >
          {/* 核心信息 */}
          <div className="space-y-1">
            <InfoRow
              icon={<Calendar size={16} />}
              label="时间范围"
              value={selectionInfo.timeRange}
              priority="high"
            />
            <InfoRow
              icon={<Clock size={16} />}
              label="总时长"
              value={selectionInfo.duration}
              priority="high"
            />
          </div>

          <Divider />

          {/* 统计信息 */}
          <div className="space-y-1">
            <InfoRow
              icon={<BarChart3 size={16} />}
              label="跨越时段"
              value={`${selectionInfo.segmentCount}个时段`}
              priority="medium"
            />
            <InfoRow
              icon={<Target size={16} />}
              label="选择块数"
              value={`${selectionInfo.blockCount}个时间块`}
              priority="medium"
            />
          </div>

          <Divider />

          {/* 建议信息 */}
          <div className="space-y-1">
            <InfoRow
              icon={<Lightbulb size={16} />}
              label="建议"
              value={selectionInfo.suggestion}
              priority="low"
            />
          </div>

          {/* 装饰性指示器 */}
          <motion.div
            className="absolute -left-2 top-1/2 -translate-y-1/2"
            initial={{ scaleY: 0 }}
            animate={{ scaleY: 1 }}
            transition={{ duration: 0.4, delay: 0.2 }}
          >
            <div
              className="w-1 h-12 rounded-full"
              style={{
                background: 'linear-gradient(to bottom, var(--primary-500), var(--primary-300))',
                opacity: 0.8,
                boxShadow: '0 0 8px var(--primary-200)'
              }}
            />
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SelectionInfoPanel;
