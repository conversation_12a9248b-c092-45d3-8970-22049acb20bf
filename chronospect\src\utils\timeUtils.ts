import { TimeBlock } from '@/types';

/**
 * 将时间槽索引转换为时间字符串
 * @param timeSlot 时间槽索引 (0-47)
 * @returns 包含开始和结束时间的对象
 */
export function formatTime(timeSlot: number): { start: string; end: string } {
  if (!isValidTimeSlot(timeSlot)) {
    throw new Error(`Invalid time slot: ${timeSlot}`);
  }

  const totalMinutes = timeSlot * 30;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  
  const startHour = hours.toString().padStart(2, '0');
  const startMinute = minutes.toString().padStart(2, '0');
  
  const endTotalMinutes = totalMinutes + 30;
  const endHours = Math.floor(endTotalMinutes / 60);
  const endMinutes = endTotalMinutes % 60;
  
  const endHour = endHours.toString().padStart(2, '0');
  const endMinuteStr = endMinutes.toString().padStart(2, '0');

  return {
    start: `${startHour}:${startMinute}`,
    end: `${endHour}:${endMinuteStr}`
  };
}

/**
 * 根据小时和分钟获取时间槽索引
 * @param hour 小时 (0-23)
 * @param minute 分钟 (0, 30)
 * @returns 时间槽索引
 */
export function getTimeSlot(hour: number, minute: number): number {
  if (hour < 0 || hour > 23) {
    throw new Error(`Invalid hour: ${hour}`);
  }
  if (minute !== 0 && minute !== 30) {
    throw new Error(`Invalid minute: ${minute}. Must be 0 or 30.`);
  }

  return hour * 2 + (minute === 30 ? 1 : 0);
}

/**
 * 验证时间槽索引是否有效
 * @param timeSlot 时间槽索引
 * @returns 是否有效
 */
export function isValidTimeSlot(timeSlot: number): boolean {
  return Number.isInteger(timeSlot) && timeSlot >= 0 && timeSlot < 48;
}

/**
 * 为指定日期生成48个空的时间块
 * @param date 日期字符串 (YYYY-MM-DD)
 * @returns 时间块数组
 */
export function generateTimeBlocks(date: string): TimeBlock[] {
  const blocks: TimeBlock[] = [];
  
  for (let i = 0; i < 48; i++) {
    const timeInfo = formatTime(i);
    blocks.push({
      id: `${date}-${i}`,
      date,
      timeSlot: i,
      activityId: null,
      startTime: timeInfo.start,
      endTime: timeInfo.end
    });
  }
  
  return blocks;
}

/**
 * 获取当前日期字符串
 * @returns YYYY-MM-DD格式的日期字符串
 */
export function getCurrentDate(): string {
  // SSR安全检查：在服务器端返回安全的默认值
  if (typeof window === 'undefined') {
    return '1970-01-01';
  }
  return new Date().toISOString().split('T')[0];
}

/**
 * 格式化日期显示
 * @param date 日期字符串 (YYYY-MM-DD)
 * @returns 格式化的日期字符串
 */
export function formatDate(date: string): string {
  const dateObj = new Date(date);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  const dateStr = dateObj.toISOString().split('T')[0];
  const todayStr = today.toISOString().split('T')[0];
  const yesterdayStr = yesterday.toISOString().split('T')[0];
  const tomorrowStr = tomorrow.toISOString().split('T')[0];

  if (dateStr === todayStr) {
    return '今天';
  } else if (dateStr === yesterdayStr) {
    return '昨天';
  } else if (dateStr === tomorrowStr) {
    return '明天';
  }

  // 返回月日格式
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekday = weekdays[dateObj.getDay()];
  
  return `${month}月${day}日 ${weekday}`;
}

/**
 * 获取日期信息
 * @param date 日期字符串 (YYYY-MM-DD)
 * @returns 日期信息对象
 */
export function getDateInfo(date: string) {
  const dateObj = new Date(date);
  const today = new Date().toISOString().split('T')[0];
  
  return {
    date,
    dayOfWeek: dateObj.getDay(),
    isToday: date === today,
    isWeekend: dateObj.getDay() === 0 || dateObj.getDay() === 6
  };
}

/**
 * 获取相邻日期
 * @param date 当前日期
 * @param offset 偏移天数 (正数为未来，负数为过去)
 * @returns 新的日期字符串
 */
export function getAdjacentDate(date: string, offset: number): string {
  const dateObj = new Date(date);
  dateObj.setDate(dateObj.getDate() + offset);
  return dateObj.toISOString().split('T')[0];
}

/**
 * 计算时间块的总分钟数
 * @param blocks 时间块数组
 * @returns 总分钟数
 */
export function calculateTotalMinutes(blocks: TimeBlock[]): number {
  return blocks.filter(block => block.activityId !== null).length * 30;
}

/**
 * 获取当前时间信息
 * @returns 当前时间的详细信息
 */
export function getCurrentTime() {
  // SSR安全检查：在服务器端返回安全的默认值
  if (typeof window === 'undefined') {
    return {
      date: '1970-01-01',
      hour: 0,
      minute: 0,
      timestamp: 0
    };
  }

  const now = new Date();
  return {
    date: now.toISOString().split('T')[0], // YYYY-MM-DD
    hour: now.getHours(),
    minute: now.getMinutes(),
    timestamp: now.getTime()
  };
}

/**
 * 获取当前时间对应的时间槽
 * @returns 当前时间槽索引 (0-47)，如果是未来时间则返回 -1
 */
export function getCurrentTimeSlot(): number {
  // SSR安全检查：在服务器端返回安全的默认值
  if (typeof window === 'undefined') {
    return 0;
  }

  const { hour, minute } = getCurrentTime();

  // 计算当前时间槽（向下取整到最近的30分钟）
  const currentSlot = hour * 2 + (minute >= 30 ? 1 : 0);

  return currentSlot;
}

/**
 * 判断指定日期和时间槽是否可编辑
 * @param date 日期字符串 (YYYY-MM-DD)
 * @param timeSlot 时间槽索引 (0-47)
 * @returns 是否可编辑
 */
export function isTimeSlotEditable(date: string, timeSlot: number): boolean {
  if (!isValidTimeSlot(timeSlot)) {
    return false;
  }

  const currentTime = getCurrentTime();
  const currentDate = currentTime.date;

  // 过去的日期都可以编辑
  if (date < currentDate) {
    return true;
  }

  // 未来的日期都不可编辑
  if (date > currentDate) {
    return false;
  }

  // 当天的情况：只有已经结束的时间槽可以编辑
  // 时间槽结束时间必须小于等于当前时间

  // 当前时间槽的结束时间
  const slotEndTime = formatTime(timeSlot);
  const [endHour, endMinute] = slotEndTime.end.split(':').map(Number);

  // 比较时间槽结束时间与当前时间
  if (endHour < currentTime.hour) {
    return true;
  } else if (endHour === currentTime.hour) {
    return endMinute <= currentTime.minute;
  } else {
    return false;
  }
}

/**
 * 获取日期的可编辑时间槽范围
 * @param date 日期字符串 (YYYY-MM-DD)
 * @returns 可编辑的时间槽范围 { start: number, end: number }
 */
export function getEditableTimeSlotRange(date: string): { start: number; end: number } {
  const currentTime = getCurrentTime();
  const currentDate = currentTime.date;

  // 过去的日期：全部可编辑
  if (date < currentDate) {
    return { start: 0, end: 47 };
  }

  // 未来的日期：全部不可编辑
  if (date > currentDate) {
    return { start: -1, end: -1 };
  }

  // 当天：从0到当前已结束的时间槽
  let editableEnd = -1;

  // 找到最后一个可编辑的时间槽
  for (let i = 0; i < 48; i++) {
    if (isTimeSlotEditable(date, i)) {
      editableEnd = i;
    } else {
      break;
    }
  }

  return { start: 0, end: editableEnd };
}

/**
 * 清理未来时间的活动数据
 * @param timeBlocks 时间块数组
 * @param date 日期字符串 (YYYY-MM-DD)
 * @returns 清理后的时间块数组
 */
export function cleanFutureTimeBlocks(timeBlocks: TimeBlock[], date: string): TimeBlock[] {
  return timeBlocks.map(block => {
    // 如果时间槽不可编辑，清除其活动数据
    if (!isTimeSlotEditable(date, block.timeSlot)) {
      return {
        ...block,
        activityId: null
      };
    }
    return block;
  });
}

/**
 * 将分钟数转换为小时分钟格式
 * @param minutes 分钟数
 * @returns 格式化的时间字符串
 */
export function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;

  if (hours === 0) {
    return `${mins}分钟`;
  } else if (mins === 0) {
    return `${hours}小时`;
  } else {
    return `${hours}小时${mins}分钟`;
  }
}

// ==================== 智能精确时间触发器工具函数 ====================

/**
 * 计算到下一个整分钟的延迟时间（毫秒）
 * 使用高精度时间计算，确保精确触发
 * @returns 到下一个整分钟的毫秒数
 */
export function calculateNextMinuteDelay(): number {
  // SSR安全检查
  if (typeof window === 'undefined') {
    return 60000; // 默认1分钟
  }

  const now = new Date();
  const nextMinute = new Date(now);

  // 设置到下一个整分钟的0秒0毫秒
  nextMinute.setSeconds(0, 0);
  nextMinute.setMinutes(nextMinute.getMinutes() + 1);

  const delay = nextMinute.getTime() - now.getTime();

  // 确保延迟时间为正数，最小为1ms
  return Math.max(delay, 1);
}

/**
 * 计算到下一个时间槽边界的延迟时间（毫秒）
 * 时间槽以30分钟为单位（00:00, 00:30, 01:00, 01:30...）
 * @returns 到下一个时间槽边界的毫秒数
 */
export function calculateNextTimeSlotDelay(): number {
  // SSR安全检查
  if (typeof window === 'undefined') {
    return 1800000; // 默认30分钟
  }

  const now = new Date();
  const currentMinute = now.getMinutes();
  const nextSlotMinute = currentMinute < 30 ? 30 : 0;

  const nextSlot = new Date(now);
  nextSlot.setSeconds(0, 0);

  if (nextSlotMinute === 0) {
    // 下一个小时的0分
    nextSlot.setHours(nextSlot.getHours() + 1, 0);
  } else {
    // 当前小时的30分
    nextSlot.setMinutes(30);
  }

  const delay = nextSlot.getTime() - now.getTime();

  // 确保延迟时间为正数，最小为1ms
  return Math.max(delay, 1);
}

/**
 * 获取高精度当前时间戳
 * 优先使用 performance.now() + performance.timeOrigin 获得高精度时间
 * @returns 高精度时间戳（毫秒）
 */
export function getHighPrecisionTimestamp(): number {
  // SSR安全检查
  if (typeof window === 'undefined') {
    return Date.now();
  }

  // 使用高精度时间API
  if (typeof performance !== 'undefined' && performance.now && performance.timeOrigin) {
    return performance.timeOrigin + performance.now();
  }

  // 降级到标准时间API
  return Date.now();
}

/**
 * 检查当前时间槽是否发生了变化
 * @param previousTimeSlot 之前的时间槽
 * @returns 是否发生变化
 */
export function hasTimeSlotChanged(previousTimeSlot: number): boolean {
  const currentTimeSlot = getCurrentTimeSlot();
  return currentTimeSlot !== previousTimeSlot;
}

/**
 * 验证时间触发器的精度
 * 用于调试和性能监控
 * @param expectedTime 期望的触发时间
 * @param actualTime 实际的触发时间
 * @returns 精度信息对象
 */
export function validateTriggerPrecision(expectedTime: number, actualTime: number) {
  const deviation = Math.abs(actualTime - expectedTime);
  const isAccurate = deviation <= 100; // 100ms内认为是精确的

  return {
    expectedTime,
    actualTime,
    deviation,
    isAccurate,
    deviationMs: deviation,
    deviationPercent: (deviation / 1000) * 100 // 相对于1秒的百分比
  };
}
