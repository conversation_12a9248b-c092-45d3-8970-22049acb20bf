# 时间块坐标系统说明文档

## 概述
本文档详细说明了时间管理系统中时间块的坐标系统和布局规格，确保所有组件使用统一的坐标转换算法。

## 布局规格

### 网格结构
- **总体布局：** 6列×8行网格
- **时间槽总数：** 48个时间槽（每个时间槽代表30分钟）
- **布局方式：** 按列优先排列

### 列的时间段分配
```
第1列：00:00-04:00 (timeSlot 0-7)
第2列：04:00-08:00 (timeSlot 8-15)
第3列：08:00-12:00 (timeSlot 16-23)
第4列：12:00-16:00 (timeSlot 24-31)
第5列：16:00-20:00 (timeSlot 32-39)
第6列：20:00-24:00 (timeSlot 40-47)
```

### 时间槽分布示意图
```
列:  0    1    2    3    4    5
行0: 0    8   16   24   32   40
行1: 1    9   17   25   33   41
行2: 2   10   18   26   34   42
行3: 3   11   19   27   35   43
行4: 4   12   20   28   36   44
行5: 5   13   21   29   37   45
行6: 6   14   22   30   38   46
行7: 7   15   23   31   39   47
```

## 坐标转换公式

### 标准转换公式（必须使用）
```typescript
// timeSlot转行列坐标
const row = timeSlot % 8;
const col = Math.floor(timeSlot / 8);

// 行列坐标转timeSlot
const timeSlot = col * 8 + row;
```

### 验证示例
```typescript
// 示例1：timeSlot 0 (00:00-00:30)
const row = 0 % 8;           // row = 0
const col = Math.floor(0/8); // col = 0
// 位置：第1列第1行

// 示例2：timeSlot 15 (07:30-08:00)
const row = 15 % 8;           // row = 7
const col = Math.floor(15/8); // col = 1
// 位置：第2列第8行

// 示例3：timeSlot 47 (23:30-24:00)
const row = 47 % 8;           // row = 7
const col = Math.floor(47/8); // col = 5
// 位置：第6列第8行
```

## 组件实现规范

### 必须遵循的规则
1. **统一坐标系统：** 所有组件必须使用相同的坐标转换公式
2. **列优先布局：** 时间槽按列优先方式排列
3. **边界检查：** 确保行列坐标不超出边界（row: 0-7, col: 0-5）
4. **时间槽范围：** timeSlot必须在0-47范围内

### 已修复的组件
- ✅ **DragPathVisualizer** (`src/components/DragPreview.tsx`)
- ✅ **TimelineIndicator** (`src/components/TimelineIndicator.tsx`)
- ✅ **dragUtils.ts** (`src/utils/dragUtils.ts`) - 已验证正确

### 正确实现参考
```typescript
// DragPathVisualizer中的正确实现
{selectedSlots.map((slot, index) => {
  // 统一坐标系统：按列优先布局 (6列×8行)
  // timeSlot转行列：row = timeSlot % 8, col = Math.floor(timeSlot / 8)
  const row = slot % 8;
  const col = Math.floor(slot / 8);
  // ... 其他逻辑
})}
```

## 常见错误

### ❌ 错误的坐标转换（行优先）
```typescript
// 错误：按行优先计算（6行×8列）
const row = Math.floor(slot / 6);
const col = slot % 6;
```

### ✅ 正确的坐标转换（列优先）
```typescript
// 正确：按列优先计算（6列×8行）
const row = slot % 8;
const col = Math.floor(slot / 8);
```

## 维护指南

### 新增组件时的注意事项
1. 涉及时间槽位置计算的组件必须使用统一的坐标转换公式
2. 添加详细的注释说明坐标系统
3. 进行充分的边界测试

### 调试技巧
1. 使用console.log输出timeSlot、row、col的对应关系
2. 验证关键时间点的坐标转换是否正确
3. 检查拖拽选择时的视觉对齐效果

## 更新历史
- **2025-07-21：** 修复DragPathVisualizer和TimelineIndicator的坐标系统不一致问题
- **2025-07-21：** 创建坐标系统说明文档
