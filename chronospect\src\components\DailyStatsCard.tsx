'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { DailyStats } from '@/types';
import { formatDuration } from '@/utils/timeUtils';
import { getUtilizationLevel, generateDailySummary } from '@/utils/statsUtils';

interface DailyStatsCardProps {
  stats: DailyStats;
}

export function DailyStatsCard({ stats }: DailyStatsCardProps) {
  const utilizationLevel = getUtilizationLevel(stats.filledPercentage);
  const summary = generateDailySummary(stats);

  return (
    <motion.div
      className="card"
      style={{
        background: 'white',
        borderRadius: 'var(--radius-xl)',
        boxShadow: 'var(--shadow-lg)',
        border: '1px solid var(--neutral-200)',
        padding: 'var(--spacing-6)'
      }}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{
        duration: 0.3,
        ease: [0.4, 0, 0.2, 1]
      }}
      whileHover={{
        boxShadow: 'var(--shadow-xl)',
        borderColor: 'var(--neutral-300)'
      }}
    >
      {/* 标题和总结 */}
      <div style={{ marginBottom: 'var(--spacing-6)' }}>
        <h2 style={{
          fontSize: 'var(--font-size-xl)',
          fontWeight: 'var(--font-weight-semibold)',
          color: 'var(--neutral-900)',
          marginBottom: 'var(--spacing-2)',
          lineHeight: 'var(--line-height-tight)'
        }}>
          📊 今日概览
        </h2>
        <p style={{
          color: 'var(--neutral-600)',
          fontSize: 'var(--font-size-base)',
          lineHeight: 'var(--line-height-normal)'
        }}>
          {summary}
        </p>
      </div>

      {/* 统计数据网格 */}
      <div className="grid grid-cols-2 md:grid-cols-4" style={{ gap: 'var(--spacing-4)' }}>
        {/* 已记录时间 */}
        <StatItem
          label="已记录时间"
          value={formatDuration(stats.filledMinutes)}
          icon="⏰"
          color="var(--primary-600)"
        />

        {/* 空白时间 */}
        <StatItem
          label="空白时间"
          value={formatDuration(stats.unfilledMinutes)}
          icon="⚪"
          color="var(--neutral-500)"
        />

        {/* 活动数量 */}
        <StatItem
          label="活动类型"
          value={`${stats.activities.length} 种`}
          icon="🎯"
          color="var(--success-600)"
        />

        {/* 时间利用率 */}
        <StatItem
          label="时间利用率"
          value={`${stats.filledPercentage.toFixed(1)}%`}
          icon="📈"
          color="var(--info-600)"
          badge={{
            text: utilizationLevel.level,
            color: utilizationLevel.color
          }}
        />
      </div>

      {/* 进度条 */}
      <div style={{ marginTop: 'var(--spacing-6)' }}>
        <div className="flex justify-between items-center" style={{ marginBottom: 'var(--spacing-2)' }}>
          <span style={{
            fontSize: 'var(--font-size-sm)',
            fontWeight: 'var(--font-weight-medium)',
            color: 'var(--neutral-700)'
          }}>
            时间填充进度
          </span>
          <span style={{
            fontSize: 'var(--font-size-sm)',
            color: 'var(--neutral-500)'
          }}>
            {stats.filledMinutes} / {stats.totalMinutes} 分钟
          </span>
        </div>
        <div style={{
          width: '100%',
          background: 'var(--neutral-200)',
          borderRadius: 'var(--radius-full)',
          height: '12px'
        }}>
          <motion.div
            style={{
              height: '12px',
              borderRadius: 'var(--radius-full)',
              background: 'linear-gradient(90deg, var(--primary-500), var(--info-500))'
            }}
            initial={{ width: 0 }}
            animate={{ width: `${stats.filledPercentage}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </div>
        <div className="flex justify-between" style={{
          fontSize: 'var(--font-size-xs)',
          color: 'var(--neutral-500)',
          marginTop: 'var(--spacing-1)'
        }}>
          <span>0%</span>
          <span>50%</span>
          <span>100%</span>
        </div>
      </div>
    </motion.div>
  );
}

// 统计项组件
interface StatItemProps {
  label: string;
  value: string;
  icon: string;
  color: string;
  badge?: {
    text: string;
    color: string;
  };
}

function StatItem({ label, value, icon, color, badge }: StatItemProps) {
  return (
    <motion.div
      className="text-center"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
    >
      <div style={{
        fontSize: 'var(--font-size-2xl)',
        marginBottom: 'var(--spacing-2)'
      }}>
        {icon}
      </div>
      <div style={{
        fontSize: 'var(--font-size-2xl)',
        fontWeight: 'var(--font-weight-bold)',
        color: color,
        marginBottom: 'var(--spacing-1)',
        lineHeight: 'var(--line-height-tight)'
      }}>
        {value}
      </div>
      <div style={{
        fontSize: 'var(--font-size-sm)',
        color: 'var(--neutral-600)',
        marginBottom: 'var(--spacing-2)',
        lineHeight: 'var(--line-height-normal)'
      }}>
        {label}
      </div>
      {badge && (
        <motion.div
          style={{
            display: 'inline-block',
            padding: 'var(--spacing-1) var(--spacing-2)',
            borderRadius: 'var(--radius-full)',
            fontSize: 'var(--font-size-xs)',
            fontWeight: 'var(--font-weight-medium)',
            color: 'white',
            backgroundColor: badge.color
          }}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, duration: 0.2, ease: [0.68, -0.55, 0.265, 1.55] }}
        >
          {badge.text}
        </motion.div>
      )}
    </motion.div>
  );
}
