'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';

/**
 * 智能时间更新器调试面板
 * 仅在开发环境中显示，用于监控时间触发器的性能
 */
export function TimeUpdaterDebugPanel() {
  const { getTimeUpdaterStats } = useAppStore();
  const [stats, setStats] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState<string>('');

  // 定期更新统计信息
  useEffect(() => {
    const updateStats = () => {
      const currentStats = getTimeUpdaterStats();
      setStats(currentStats);
      setLastUpdateTime(new Date().toLocaleTimeString());
    };

    // 立即更新一次
    updateStats();

    // 每秒更新一次统计信息
    const interval = setInterval(updateStats, 1000);

    return () => clearInterval(interval);
  }, [getTimeUpdaterStats]);

  // 只在开发环境中显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  if (!stats) {
    return null;
  }

  const accuracyRate = stats.totalTriggers > 0 
    ? ((stats.accurateTriggers / stats.totalTriggers) * 100).toFixed(1)
    : '0';

  return (
    <>
      {/* 切换按钮 */}
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="时间更新器调试面板"
      >
        🕐
      </motion.button>

      {/* 调试面板 */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed bottom-16 right-4 z-40 bg-white border border-gray-300 rounded-lg shadow-xl p-4 w-80"
            style={{
              fontSize: '12px',
              fontFamily: 'monospace'
            }}
          >
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-bold text-sm text-gray-800">时间更新器状态</h3>
              <button
                onClick={() => setIsVisible(false)}
                className="text-gray-500 hover:text-gray-700 text-lg leading-none"
              >
                ×
              </button>
            </div>

            <div className="space-y-2 text-xs">
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="text-gray-600">总触发次数:</span>
                  <div className="font-mono font-bold text-blue-600">
                    {stats.totalTriggers}
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">精确触发:</span>
                  <div className="font-mono font-bold text-green-600">
                    {stats.accurateTriggers} ({accuracyRate}%)
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="text-gray-600">平均偏差:</span>
                  <div className="font-mono font-bold text-orange-600">
                    {stats.averageDeviation.toFixed(1)}ms
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">最大偏差:</span>
                  <div className="font-mono font-bold text-red-600">
                    {stats.maxDeviation.toFixed(1)}ms
                  </div>
                </div>
              </div>

              <div>
                <span className="text-gray-600">跳过触发:</span>
                <div className="font-mono font-bold text-yellow-600">
                  {stats.skippedTriggers}
                </div>
              </div>

              <div>
                <span className="text-gray-600">最后触发:</span>
                <div className="font-mono text-gray-800">
                  {stats.lastTriggerTime > 0 
                    ? new Date(stats.lastTriggerTime).toLocaleTimeString()
                    : '未触发'
                  }
                </div>
              </div>

              <div className="pt-2 border-t border-gray-200">
                <span className="text-gray-600">面板更新:</span>
                <div className="font-mono text-gray-800">
                  {lastUpdateTime}
                </div>
              </div>

              {/* 性能指标 */}
              <div className="pt-2 border-t border-gray-200">
                <div className="text-xs text-gray-600 mb-1">性能评级:</div>
                <div className="flex items-center space-x-2">
                  <div 
                    className={`w-3 h-3 rounded-full ${
                      parseFloat(accuracyRate) >= 95 ? 'bg-green-500' :
                      parseFloat(accuracyRate) >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                  />
                  <span className="text-xs">
                    {parseFloat(accuracyRate) >= 95 ? '优秀' :
                     parseFloat(accuracyRate) >= 80 ? '良好' : '需要优化'}
                  </span>
                </div>
              </div>
            </div>

            {/* 说明 */}
            <div className="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500">
              <div>• 精确触发: 偏差 ≤ 100ms</div>
              <div>• 智能触发器在分钟边界精确更新</div>
              <div>• 页面隐藏时会跳过触发以节省资源</div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
