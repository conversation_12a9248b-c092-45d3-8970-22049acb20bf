'use client';

import { useCallback, useRef, useTransition } from 'react';
import { useAppStore } from '@/stores/useAppStore';

export type ViewType = 'grid' | 'review';

interface ViewPreloaderOptions {
  preloadDelay?: number; // hover预加载延迟（毫秒）
  enableHoverPreload?: boolean; // 是否启用hover预加载
}

interface ViewPreloaderReturn {
  isPreloading: boolean;
  isPending: boolean;
  preloadView: (view: ViewType) => Promise<void>;
  switchView: (view: ViewType) => void;
  handleMouseEnter: (view: ViewType) => void;
  handleMouseLeave: () => void;
}

/**
 * 智能视图预加载Hook
 * 提供hover预加载和无缝视图切换功能
 */
export function useViewPreloader(options: ViewPreloaderOptions = {}): ViewPreloaderReturn {
  const {
    preloadDelay = 300,
    enableHoverPreload = true
  } = options;

  const {
    currentDate,
    getDailyStatsAsync,
    setCurrentView,
    ui,
    setViewPreloading,
    setViewPreloaded,
    isViewPreloaded,
    isViewPreloading
  } = useAppStore();

  const [isPending, startTransition] = useTransition();
  const preloadTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const preloadingRef = useRef<Set<string>>(new Set());

  /**
   * 预加载指定视图的数据
   */
  const preloadView = useCallback(async (view: ViewType): Promise<void> => {
    const preloadKey = `${view}-${currentDate}`;

    // 避免重复预加载
    if (isViewPreloading(preloadKey) || isViewPreloaded(preloadKey)) {
      return;
    }

    setViewPreloading(preloadKey, true);

    try {
      if (view === 'review') {
        // 预加载复盘视图需要的数据
        await getDailyStatsAsync(currentDate);

        // 预加载相邻日期的数据（用于日期对比）
        const yesterday = new Date(currentDate);
        yesterday.setDate(yesterday.getDate() - 1);
        const tomorrow = new Date(currentDate);
        tomorrow.setDate(tomorrow.getDate() + 1);

        await Promise.all([
          getDailyStatsAsync(yesterday.toISOString().split('T')[0]),
          getDailyStatsAsync(tomorrow.toISOString().split('T')[0])
        ]);
      }
      // grid视图通常不需要额外预加载，因为数据已经在当前页面中

      setViewPreloaded(preloadKey, true);
    } catch (error) {
      console.warn(`预加载视图 ${view} 失败:`, error);
    } finally {
      setViewPreloading(preloadKey, false);
    }
  }, [currentDate, getDailyStatsAsync, setViewPreloading, setViewPreloaded, isViewPreloading, isViewPreloaded]);

  /**
   * 切换视图（带预加载）
   */
  const switchView = useCallback((view: ViewType) => {
    if (ui.currentView === view) return;

    startTransition(async () => {
      // 先预加载数据，再切换视图
      await preloadView(view);
      setCurrentView(view);
    });
  }, [ui.currentView, preloadView, setCurrentView]);

  /**
   * 处理鼠标进入事件（hover预加载）
   */
  const handleMouseEnter = useCallback((view: ViewType) => {
    if (!enableHoverPreload || ui.currentView === view) return;

    // 清除之前的定时器
    if (preloadTimeoutRef.current) {
      clearTimeout(preloadTimeoutRef.current);
    }

    // 延迟预加载，避免鼠标快速划过时的无效预加载
    preloadTimeoutRef.current = setTimeout(() => {
      preloadView(view);
    }, preloadDelay);
  }, [enableHoverPreload, ui.currentView, preloadView, preloadDelay]);

  /**
   * 处理鼠标离开事件
   */
  const handleMouseLeave = useCallback(() => {
    if (preloadTimeoutRef.current) {
      clearTimeout(preloadTimeoutRef.current);
      preloadTimeoutRef.current = null;
    }
  }, []);

  return {
    isPreloading: isViewPreloading(`${ui.currentView}-${currentDate}`),
    isPending,
    preloadView,
    switchView,
    handleMouseEnter,
    handleMouseLeave
  };
}
