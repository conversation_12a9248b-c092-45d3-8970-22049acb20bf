# ActivityPalette改造任务

## 任务目标
将当前的ActivityPalette组件从全屏模态框改为轻量级的右侧固定浮层，提升用户交互体验。

## 需求详情
1. **浮层定位**：将浮层固定在界面右侧，而不是居中的全屏模态框
2. **触发时机**：当用户选中一个或多个时间区块时立即显示活动选择浮层
3. **视觉设计**：
   - 移除全屏黑色遮罩背景
   - 使用轻量级的卡片式设计
   - 保持简洁的活动列表显示（颜色、图标、名称）
   - 添加适当的阴影和圆角以突出浮层效果
4. **交互体验**：
   - 保持当前的两次点击流程（点击区块→选择活动）
   - 选择活动后立即填充区块并关闭浮层
   - 点击浮层外部区域或ESC键可关闭浮层
5. **兼容性**：确保与现有的30分钟时间块、颜色编码系统、拖拽多选功能完全兼容

## 实施方案
采用右侧固定浮层设计（方案A）：
- 位置：固定在界面右侧，距离边缘16px
- 尺寸：宽度280px，高度自适应（最大70vh）
- 动画：从右侧滑入，带有弹性效果

## 执行计划
1. 分析现有代码结构
2. 重构ActivityPalette组件
3. 优化交互体验
4. 样式细节调整
5. 兼容性测试
6. 性能优化

## 时间节点
- 开始时间：2025-07-18
- 第一次改造完成：2025-07-18 (右侧浮层)
- 第二次改造开始：2025-07-18 (极简水平工具栏)

## 第二次改造需求
基于用户反馈，右侧浮层存在UI遮挡问题且过于臃肿，需要改为极简水平工具栏：

### 问题分析
1. **UI遮挡问题** - 右侧浮层遮挡时间网格，特别是小屏幕
2. **拖拽选择Bug** - 拖拽多选后活动选择界面不出现
3. **设计臃肿** - 当前浮层包含过多元素，不够简洁

### 新设计方案：极简水平工具栏
- **位置**：时间网格正上方，紧贴网格边缘
- **样式**：半透明白色背景，轻微模糊效果，圆角设计
- **内容**：只显示颜色圆点 + 活动名称，去掉图标、描述、按钮
- **布局**：水平排列，自动换行，响应式设计
- **动画**：从上方轻柔滑入，带有弹性效果
