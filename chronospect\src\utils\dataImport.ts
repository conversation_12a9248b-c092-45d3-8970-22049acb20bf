/**
 * 数据导入工具函数
 * 支持JSON格式的数据导入和智能合并
 */

import type { Activity, TimeBlock, DailyStats } from '@/types';
import type { ExportData } from './dataExport';

// 导入策略枚举
export enum ImportStrategy {
  OVERWRITE = 'overwrite',    // 覆盖现有数据
  MERGE = 'merge',           // 智能合并（默认）
  ADD_ONLY = 'add_only'      // 仅添加新数据
}

// 导入结果
export interface ImportResult {
  success: boolean;
  message: string;
  statistics: {
    activitiesAdded: number;
    activitiesUpdated: number;
    timeBlocksAdded: number;
    timeBlocksUpdated: number;
    daysImported: number;
  };
  conflicts?: ImportConflict[];
}

// 导入冲突
export interface ImportConflict {
  type: 'activity' | 'timeBlock';
  id: string;
  field: string;
  existingValue: any;
  newValue: any;
  resolution: 'keep_existing' | 'use_new' | 'merge';
}

// 数据验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  data?: ExportData;
}

/**
 * 验证导入文件格式
 */
export function validateImportData(content: string): ValidationResult {
  const result: ValidationResult = {
    isValid: false,
    errors: [],
    warnings: []
  };

  try {
    const data = JSON.parse(content) as ExportData;
    
    // 检查基本结构
    if (!data.version) {
      result.errors.push('缺少版本信息');
    }
    
    if (!data.data) {
      result.errors.push('缺少数据部分');
      return result;
    }

    // 检查活动数据
    if (!Array.isArray(data.data.activities)) {
      result.errors.push('活动数据格式错误');
    } else {
      data.data.activities.forEach((activity, index) => {
        if (!activity.id || !activity.name || !activity.color) {
          result.errors.push(`活动 ${index + 1} 缺少必要字段`);
        }
      });
    }

    // 检查时间块数据
    if (typeof data.data.timeBlocks !== 'object') {
      result.errors.push('时间块数据格式错误');
    } else {
      Object.entries(data.data.timeBlocks).forEach(([date, blocks]) => {
        if (!Array.isArray(blocks)) {
          result.errors.push(`日期 ${date} 的时间块数据格式错误`);
        } else {
          blocks.forEach((block, index) => {
            if (!block.id || !block.date || typeof block.timeSlot !== 'number') {
              result.errors.push(`日期 ${date} 的时间块 ${index + 1} 缺少必要字段`);
            }
          });
        }
      });
    }

    // 检查统计数据
    if (data.data.dailyStats && typeof data.data.dailyStats !== 'object') {
      result.warnings.push('统计数据格式可能有问题，将跳过导入');
    }

    // 版本兼容性检查
    if (data.version !== '1.0.0') {
      result.warnings.push(`数据版本 ${data.version} 可能不完全兼容当前版本`);
    }

    if (result.errors.length === 0) {
      result.isValid = true;
      result.data = data;
    }

  } catch (error) {
    result.errors.push('JSON格式错误：' + (error as Error).message);
  }

  return result;
}

/**
 * 检测导入冲突
 */
export function detectConflicts(
  importData: ExportData,
  existingActivities: Activity[],
  existingTimeBlocks: Record<string, TimeBlock[]>
): ImportConflict[] {
  const conflicts: ImportConflict[] = [];

  // 检测活动冲突
  const existingActivityMap = new Map(existingActivities.map(a => [a.id, a]));
  
  importData.data.activities.forEach(importActivity => {
    const existing = existingActivityMap.get(importActivity.id);
    if (existing) {
      // 检查名称冲突
      if (existing.name !== importActivity.name) {
        conflicts.push({
          type: 'activity',
          id: importActivity.id,
          field: 'name',
          existingValue: existing.name,
          newValue: importActivity.name,
          resolution: 'keep_existing'
        });
      }
      
      // 检查颜色冲突
      if (existing.color !== importActivity.color) {
        conflicts.push({
          type: 'activity',
          id: importActivity.id,
          field: 'color',
          existingValue: existing.color,
          newValue: importActivity.color,
          resolution: 'keep_existing'
        });
      }
    }
  });

  // 检测时间块冲突
  Object.entries(importData.data.timeBlocks).forEach(([date, importBlocks]) => {
    const existingBlocks = existingTimeBlocks[date] || [];
    const existingBlockMap = new Map(existingBlocks.map(b => [b.timeSlot, b]));

    importBlocks.forEach(importBlock => {
      const existing = existingBlockMap.get(importBlock.timeSlot);
      if (existing && existing.activityId !== importBlock.activityId) {
        conflicts.push({
          type: 'timeBlock',
          id: `${date}-${importBlock.timeSlot}`,
          field: 'activityId',
          existingValue: existing.activityId,
          newValue: importBlock.activityId,
          resolution: 'keep_existing'
        });
      }
    });
  });

  return conflicts;
}

/**
 * 执行数据导入
 */
export function executeImport(
  importData: ExportData,
  existingActivities: Activity[],
  existingTimeBlocks: Record<string, TimeBlock[]>,
  existingDailyStats: Record<string, DailyStats>,
  strategy: ImportStrategy = ImportStrategy.MERGE
): ImportResult {
  const result: ImportResult = {
    success: false,
    message: '',
    statistics: {
      activitiesAdded: 0,
      activitiesUpdated: 0,
      timeBlocksAdded: 0,
      timeBlocksUpdated: 0,
      daysImported: 0
    }
  };

  try {
    let newActivities = [...existingActivities];
    let newTimeBlocks = { ...existingTimeBlocks };
    let newDailyStats = { ...existingDailyStats };

    // 处理活动数据
    const existingActivityMap = new Map(existingActivities.map(a => [a.id, a]));
    
    importData.data.activities.forEach(importActivity => {
      const existing = existingActivityMap.get(importActivity.id);
      
      if (existing) {
        if (strategy === ImportStrategy.OVERWRITE || strategy === ImportStrategy.MERGE) {
          // 更新现有活动
          const index = newActivities.findIndex(a => a.id === importActivity.id);
          if (index !== -1) {
            newActivities[index] = {
              ...existing,
              ...importActivity,
              updatedAt: new Date().toISOString()
            };
            result.statistics.activitiesUpdated++;
          }
        }
      } else {
        // 添加新活动
        newActivities.push(importActivity);
        result.statistics.activitiesAdded++;
      }
    });

    // 处理时间块数据
    Object.entries(importData.data.timeBlocks).forEach(([date, importBlocks]) => {
      const existingBlocks = newTimeBlocks[date] || [];
      const existingBlockMap = new Map(existingBlocks.map(b => [b.timeSlot, b]));

      if (strategy === ImportStrategy.OVERWRITE) {
        // 覆盖整天的数据
        newTimeBlocks[date] = importBlocks;
        result.statistics.timeBlocksAdded += importBlocks.length;
      } else {
        // 合并或仅添加
        const mergedBlocks = [...existingBlocks];
        
        importBlocks.forEach(importBlock => {
          const existingIndex = mergedBlocks.findIndex(b => b.timeSlot === importBlock.timeSlot);
          
          if (existingIndex !== -1) {
            if (strategy === ImportStrategy.MERGE) {
              // 智能合并：如果现有块为空，则使用导入的数据
              if (!mergedBlocks[existingIndex].activityId && importBlock.activityId) {
                mergedBlocks[existingIndex] = importBlock;
                result.statistics.timeBlocksUpdated++;
              }
            }
          } else {
            // 添加新的时间块
            mergedBlocks.push(importBlock);
            result.statistics.timeBlocksAdded++;
          }
        });

        newTimeBlocks[date] = mergedBlocks.sort((a, b) => a.timeSlot - b.timeSlot);
      }

      result.statistics.daysImported++;
    });

    // 处理统计数据（如果存在）
    if (importData.data.dailyStats) {
      Object.entries(importData.data.dailyStats).forEach(([date, stats]) => {
        if (strategy === ImportStrategy.OVERWRITE || !newDailyStats[date]) {
          newDailyStats[date] = stats;
        }
      });
    }

    result.success = true;
    result.message = '数据导入成功';

    // 返回处理后的数据（通过闭包或其他方式传递给调用者）
    (result as any).data = {
      activities: newActivities,
      timeBlocks: newTimeBlocks,
      dailyStats: newDailyStats
    };

  } catch (error) {
    result.success = false;
    result.message = '导入过程中发生错误：' + (error as Error).message;
  }

  return result;
}

/**
 * 读取文件内容
 */
export function readFileContent(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const content = event.target?.result as string;
      resolve(content);
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    
    reader.readAsText(file, 'utf-8');
  });
}
