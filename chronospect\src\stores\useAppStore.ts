import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Activity, TimeBlock, UIState, DEFAULT_ACTIVITIES, DEFAULT_COLORS, DailyStats } from '@/types';
import { generateTimeBlocks, getCurrentDate, getAdjacentDate, getCurrentTimeSlot, cleanFutureTimeBlocks } from '@/utils/timeUtils';
import { calculateDailyStats } from '@/utils/statsUtils';
import { StatisticalLRUCache, DataPreloader, MemoryMonitor, getAdjacentDates } from '@/utils/cacheUtils';
import { LoadingManager, LoadingState } from '@/utils/loadingUtils';
import { SmartTimeUpdater, type SmartTimeUpdaterOptions } from '@/utils/smartTimeUpdater';
import { ImportStrategy, type ImportResult } from '@/utils/dataImport';

interface AppStore {
  // 状态
  currentDate: string;
  activities: Activity[];
  timeBlocks: Record<string, TimeBlock[]>;
  ui: UIState;
  dailyStats: Record<string, DailyStats>; // 按日期缓存的统计数据
  loadingStates: Record<string, LoadingState>; // 加载状态管理
  currentTimeSlot: number; // 当前时间槽，用于实时更新可编辑状态
  currentTime: { date: string; hour: number; minute: number; second: number }; // 当前时间信息，用于实时显示

  // 数据导入导出状态
  dataManagement: {
    isExporting: boolean;
    isImporting: boolean;
    showDropdown: boolean;
    importProgress: number;
    exportProgress: number;
    lastImportResult?: ImportResult;
  };

  // 视图预加载状态
  viewPreloader: {
    preloadingViews: Set<string>; // 正在预加载的视图
    preloadedViews: Set<string>; // 已预加载完成的视图
    lastPreloadTime: Record<string, number>; // 最后预加载时间
  };

  // 日期相关操作
  setCurrentDate: (date: string) => void;
  goToPreviousDay: () => void;
  goToNextDay: () => void;
  goToToday: () => void;

  // 活动相关操作
  addActivity: (activity: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateActivity: (id: string, updates: Partial<Activity>) => void;
  deleteActivity: (id: string) => void;
  getActivityById: (id: string) => Activity | undefined;

  // 时间块相关操作
  getTimeBlocksForDate: (date: string) => TimeBlock[];
  updateTimeBlock: (date: string, timeSlot: number, activityId: string | null) => void;
  updateMultipleTimeBlocks: (date: string, timeSlots: number[], activityId: string | null) => void;
  clearTimeBlock: (date: string, timeSlot: number) => void;
  clearMultipleTimeBlocks: (date: string, timeSlots: number[]) => void;

  // 统计相关操作
  getDailyStats: (date: string) => DailyStats;
  getDailyStatsSync: (date: string) => DailyStats | null; // 纯读取函数，不触发状态更新
  getDailyStatsAsync: (date: string) => Promise<DailyStats>; // 异步获取统计数据
  invalidateStats: (date: string) => void; // 使指定日期的统计缓存失效
  invalidateAllStats: () => void; // 使所有统计缓存失效
  preloadAdjacentDates: (date: string) => void; // 预加载相邻日期数据

  // 加载状态管理
  setLoadingState: (key: string, state: LoadingState) => void;
  getLoadingState: (key: string) => LoadingState;

  // UI状态操作
  setSelectedBlocks: (blocks: number[]) => void;
  addSelectedBlock: (block: number) => void;
  removeSelectedBlock: (block: number) => void;
  clearSelectedBlocks: () => void;
  setShowActivityPalette: (show: boolean) => void;
  setCurrentView: (view: 'grid' | 'review') => void;
  setDragging: (isDragging: boolean, startBlock?: number) => void;

  // 视图预加载相关操作
  setViewPreloading: (viewKey: string, isPreloading: boolean) => void;
  setViewPreloaded: (viewKey: string, isPreloaded: boolean) => void;
  isViewPreloaded: (viewKey: string) => boolean;
  isViewPreloading: (viewKey: string) => boolean;

  // 时间相关操作
  updateCurrentTime: () => void;
  startTimeUpdater: () => void;
  stopTimeUpdater: () => void;
  getTimeUpdaterStats: () => any; // 获取时间更新器统计信息

  // 初始化操作
  initializeApp: () => void;

  // 数据导入导出操作
  setDataManagementDropdown: (show: boolean) => void;
  setExportProgress: (progress: number) => void;
  setImportProgress: (progress: number) => void;
  setIsExporting: (isExporting: boolean) => void;
  setIsImporting: (isImporting: boolean) => void;
  setLastImportResult: (result: ImportResult) => void;
  exportData: (format: 'json' | 'csv', dateRange?: { start: string; end: string }) => Promise<void>;
  importData: (file: File, strategy?: ImportStrategy) => Promise<ImportResult>;
}

// 生成唯一ID
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 创建默认活动
function createDefaultActivities(): Activity[] {
  const now = new Date().toISOString();
  return DEFAULT_ACTIVITIES.map(activity => ({
    ...activity,
    id: generateId(),
    createdAt: now,
    updatedAt: now
  }));
}

// 创建全局缓存和管理器实例
const statsCache = new StatisticalLRUCache<string, DailyStats>(100);
const dataPreloader = DataPreloader.getInstance();
const memoryMonitor = MemoryMonitor.getInstance();
const loadingManager = LoadingManager.getInstance();

// 智能时间更新器实例（延迟初始化）
let smartTimeUpdater: SmartTimeUpdater | null = null;

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentDate: getCurrentDate(),
      activities: [],
      timeBlocks: {},
      dailyStats: {},
      loadingStates: {},
      currentTimeSlot: typeof window !== 'undefined' ? getCurrentTimeSlot() : 0,
      currentTime: (() => {
        // 在SSR期间使用安全的默认值，避免hydration mismatch
        if (typeof window === 'undefined') {
          return {
            date: '1970-01-01', // 安全的默认日期
            hour: 0,
            minute: 0,
            second: 0
          };
        }
        const now = new Date();
        return {
          date: now.toISOString().split('T')[0],
          hour: now.getHours(),
          minute: now.getMinutes(),
          second: now.getSeconds()
        };
      })(),
      ui: {
        selectedBlocks: [],
        showActivityPalette: false,
        currentView: 'grid',
        isDragging: false,
        dragStartBlock: null
      },
      dataManagement: {
        isExporting: false,
        isImporting: false,
        showDropdown: false,
        importProgress: 0,
        exportProgress: 0,
        lastImportResult: undefined
      },
      viewPreloader: {
        preloadingViews: new Set(),
        preloadedViews: new Set(),
        lastPreloadTime: {}
      },

      // 日期相关操作
      setCurrentDate: (date: string) => {
        set({ currentDate: date });

        // 立即更新当前时间槽（用于正确计算可编辑状态）
        get().updateCurrentTime();

        // 确保该日期的时间块存在
        const state = get();
        if (!state.timeBlocks[date]) {
          set(state => ({
            timeBlocks: {
              ...state.timeBlocks,
              [date]: generateTimeBlocks(date)
            }
          }));
        }

        // 预加载相邻日期数据
        get().preloadAdjacentDates(date);
      },

      goToPreviousDay: () => {
        const currentDate = get().currentDate;
        const prevDate = new Date(currentDate);
        prevDate.setDate(prevDate.getDate() - 1);
        get().setCurrentDate(prevDate.toISOString().split('T')[0]);
      },

      goToNextDay: () => {
        const currentDate = get().currentDate;
        const nextDate = new Date(currentDate);
        nextDate.setDate(nextDate.getDate() + 1);
        get().setCurrentDate(nextDate.toISOString().split('T')[0]);
      },

      goToToday: () => {
        get().setCurrentDate(getCurrentDate());
      },

      // 活动相关操作
      addActivity: (activityData) => {
        const now = new Date().toISOString();
        const newActivity: Activity = {
          ...activityData,
          id: generateId(),
          createdAt: now,
          updatedAt: now
        };

        set(state => ({
          activities: [...state.activities, newActivity]
        }));
      },

      updateActivity: (id: string, updates: Partial<Activity>) => {
        set(state => ({
          activities: state.activities.map(activity =>
            activity.id === id
              ? { ...activity, ...updates, updatedAt: new Date().toISOString() }
              : activity
          )
        }));
      },

      deleteActivity: (id: string) => {
        set(state => ({
          activities: state.activities.filter(activity => activity.id !== id),
          // 清除所有使用该活动的时间块
          timeBlocks: Object.fromEntries(
            Object.entries(state.timeBlocks).map(([date, blocks]) => [
              date,
              blocks.map(block =>
                block.activityId === id ? { ...block, activityId: null } : block
              )
            ])
          )
        }));
      },

      getActivityById: (id: string) => {
        return get().activities.find(activity => activity.id === id);
      },

      // 时间块相关操作
      getTimeBlocksForDate: (date: string) => {
        const state = get();
        if (!state.timeBlocks[date]) {
          const blocks = generateTimeBlocks(date);
          set(state => ({
            timeBlocks: {
              ...state.timeBlocks,
              [date]: blocks
            }
          }));
          return blocks;
        }

        // 返回时清理未来时间的数据
        const cleanedBlocks = cleanFutureTimeBlocks(state.timeBlocks[date], date);

        // 如果数据被清理了，更新存储
        if (JSON.stringify(cleanedBlocks) !== JSON.stringify(state.timeBlocks[date])) {
          set(state => ({
            timeBlocks: {
              ...state.timeBlocks,
              [date]: cleanedBlocks
            }
          }));
        }

        return cleanedBlocks;
      },

      updateTimeBlock: (date: string, timeSlot: number, activityId: string | null) => {
        set(state => ({
          timeBlocks: {
            ...state.timeBlocks,
            [date]: state.timeBlocks[date]?.map(block =>
              block.timeSlot === timeSlot
                ? { ...block, activityId }
                : block
            ) || generateTimeBlocks(date).map(block =>
              block.timeSlot === timeSlot
                ? { ...block, activityId }
                : block
            )
          },
          // 使该日期的统计缓存失效
          dailyStats: (() => {
            const newDailyStats = { ...state.dailyStats };
            delete newDailyStats[date];
            return newDailyStats;
          })()
        }));
      },

      updateMultipleTimeBlocks: (date: string, timeSlots: number[], activityId: string | null) => {
        set(state => ({
          timeBlocks: {
            ...state.timeBlocks,
            [date]: state.timeBlocks[date]?.map(block =>
              timeSlots.includes(block.timeSlot)
                ? { ...block, activityId }
                : block
            ) || generateTimeBlocks(date).map(block =>
              timeSlots.includes(block.timeSlot)
                ? { ...block, activityId }
                : block
            )
          },
          // 使该日期的统计缓存失效
          dailyStats: (() => {
            const newDailyStats = { ...state.dailyStats };
            delete newDailyStats[date];
            return newDailyStats;
          })()
        }));
      },

      clearTimeBlock: (date: string, timeSlot: number) => {
        get().updateTimeBlock(date, timeSlot, null);
      },

      clearMultipleTimeBlocks: (date: string, timeSlots: number[]) => {
        get().updateMultipleTimeBlocks(date, timeSlots, null);
      },

      // 统计相关操作
      getDailyStats: (date: string) => {
        const state = get();

        // 先检查LRU缓存
        const cachedStats = statsCache.get(date);
        if (cachedStats) {
          return cachedStats;
        }

        // 检查状态缓存
        if (state.dailyStats[date]) {
          statsCache.set(date, state.dailyStats[date]);
          return state.dailyStats[date];
        }

        // 计算统计数据
        const timeBlocks = state.timeBlocks[date] || generateTimeBlocks(date);
        const stats = calculateDailyStats(timeBlocks, state.activities);

        // 缓存到LRU缓存
        statsCache.set(date, stats);

        // 异步更新状态缓存，避免在渲染过程中同步更新状态
        setTimeout(() => {
          set(state => ({
            dailyStats: {
              ...state.dailyStats,
              [date]: stats
            }
          }));
        }, 0);

        return stats;
      },

      // 纯读取函数，不触发任何状态更新
      getDailyStatsSync: (date: string) => {
        const state = get();

        // 先检查LRU缓存
        const cachedStats = statsCache.get(date);
        if (cachedStats) {
          return cachedStats;
        }

        // 检查状态缓存
        if (state.dailyStats[date]) {
          statsCache.set(date, state.dailyStats[date]);
          return state.dailyStats[date];
        }

        // 如果没有缓存，返回null而不是计算
        return null;
      },

      getDailyStatsAsync: async (date: string) => {
        const loadingKey = `stats-${date}`;

        try {
          get().setLoadingState(loadingKey, 'loading');

          // 模拟异步计算（对于复杂统计）
          await new Promise(resolve => setTimeout(resolve, 50));

          const stats = get().getDailyStats(date);
          get().setLoadingState(loadingKey, 'success');

          return stats;
        } catch (error) {
          get().setLoadingState(loadingKey, 'error');
          throw error;
        }
      },

      invalidateStats: (date: string) => {
        // 从两个缓存中删除
        statsCache.delete(date);
        set(state => {
          const newDailyStats = { ...state.dailyStats };
          delete newDailyStats[date];
          return { dailyStats: newDailyStats };
        });
      },

      invalidateAllStats: () => {
        // 清空所有缓存
        statsCache.clear();
        set({ dailyStats: {} });
      },

      preloadAdjacentDates: (date: string) => {
        // 检查内存使用情况
        if (memoryMonitor.checkMemoryUsage()) {
          console.warn('内存使用过高，跳过预加载');
          return;
        }

        const adjacentDates = getAdjacentDates(date, 2);
        dataPreloader.addMultipleToQueue(adjacentDates);
      },

      // 加载状态管理
      setLoadingState: (key: string, state: LoadingState) => {
        set(prevState => ({
          loadingStates: {
            ...prevState.loadingStates,
            [key]: state
          }
        }));
        loadingManager.setLoadingState(key, state);
      },

      getLoadingState: (key: string) => {
        const state = get();
        return state.loadingStates[key] || 'idle';
      },

      // UI状态操作
      setSelectedBlocks: (blocks: number[]) => {
        set(state => ({
          ui: { ...state.ui, selectedBlocks: blocks }
        }));
      },

      addSelectedBlock: (block: number) => {
        set(state => ({
          ui: {
            ...state.ui,
            selectedBlocks: [...state.ui.selectedBlocks, block]
          }
        }));
      },

      removeSelectedBlock: (block: number) => {
        set(state => ({
          ui: {
            ...state.ui,
            selectedBlocks: state.ui.selectedBlocks.filter(b => b !== block)
          }
        }));
      },

      clearSelectedBlocks: () => {
        set(state => ({
          ui: { ...state.ui, selectedBlocks: [] }
        }));
      },

      setShowActivityPalette: (show: boolean) => {
        set(state => ({
          ui: { ...state.ui, showActivityPalette: show }
        }));
      },

      setCurrentView: (view: 'grid' | 'review') => {
        set(state => ({
          ui: { ...state.ui, currentView: view }
        }));
      },

      // 视图预加载相关方法
      setViewPreloading: (viewKey: string, isPreloading: boolean) => {
        set(state => {
          const newPreloadingViews = new Set(state.viewPreloader.preloadingViews);
          if (isPreloading) {
            newPreloadingViews.add(viewKey);
          } else {
            newPreloadingViews.delete(viewKey);
          }
          return {
            viewPreloader: {
              ...state.viewPreloader,
              preloadingViews: newPreloadingViews
            }
          };
        });
      },

      setViewPreloaded: (viewKey: string, isPreloaded: boolean) => {
        set(state => {
          const newPreloadedViews = new Set(state.viewPreloader.preloadedViews);
          const newLastPreloadTime = { ...state.viewPreloader.lastPreloadTime };

          if (isPreloaded) {
            newPreloadedViews.add(viewKey);
            newLastPreloadTime[viewKey] = Date.now();
          } else {
            newPreloadedViews.delete(viewKey);
            delete newLastPreloadTime[viewKey];
          }

          return {
            viewPreloader: {
              ...state.viewPreloader,
              preloadedViews: newPreloadedViews,
              lastPreloadTime: newLastPreloadTime
            }
          };
        });
      },

      isViewPreloaded: (viewKey: string) => {
        const state = get();
        return state.viewPreloader.preloadedViews.has(viewKey);
      },

      isViewPreloading: (viewKey: string) => {
        const state = get();
        return state.viewPreloader.preloadingViews.has(viewKey);
      },

      setDragging: (isDragging: boolean, startBlock?: number) => {
        set(state => ({
          ui: {
            ...state.ui,
            isDragging,
            dragStartBlock: isDragging ? (startBlock ?? null) : null
          }
        }));
      },

      // 时间相关操作
      updateCurrentTime: () => {
        const now = new Date();
        const newCurrentTime = {
          date: now.toISOString().split('T')[0],
          hour: now.getHours(),
          minute: now.getMinutes(),
          second: now.getSeconds()
        };
        const newTimeSlot = getCurrentTimeSlot();
        const currentState = get();

        // 总是更新currentTime以确保时间显示实时性
        // 只有当时间槽发生变化时才更新其他状态
        if (newTimeSlot !== currentState.currentTimeSlot) {
          set({
            currentTime: newCurrentTime,
            currentTimeSlot: newTimeSlot
          });

          // 时间槽变化时，清理当前日期的未来时间数据
          const currentDate = currentState.currentDate;
          if (currentState.timeBlocks[currentDate]) {
            const cleanedBlocks = cleanFutureTimeBlocks(currentState.timeBlocks[currentDate], currentDate);
            if (JSON.stringify(cleanedBlocks) !== JSON.stringify(currentState.timeBlocks[currentDate])) {
              set(state => ({
                timeBlocks: {
                  ...state.timeBlocks,
                  [currentDate]: cleanedBlocks
                }
              }));
            }
          }
        } else {
          // 即使时间槽没变化，也要更新currentTime以保持时间显示的实时性
          set({ currentTime: newCurrentTime });
        }
      },

      startTimeUpdater: () => {
        // 避免重复启动智能时间更新器
        if (typeof window !== 'undefined' && !smartTimeUpdater) {
          // 创建智能时间更新器实例
          smartTimeUpdater = new SmartTimeUpdater({
            onTimeUpdate: () => {
              console.log('[SmartTimeUpdater] Triggering time update at:', new Date().toLocaleTimeString());
              get().updateCurrentTime();
            },
            debug: true, // 启用调试模式查看触发情况
            useTimeSlotBoundary: false, // 使用分钟边界触发，更精确
            syncOnVisibilityChange: true, // 页面可见性变化时重新同步
            recoveryCheckInterval: 5 * 60 * 1000 // 5分钟错误恢复检查
          });

          // 启动智能时间更新器
          smartTimeUpdater.start();

          // 在开发环境中添加全局调试函数
          if (typeof window !== 'undefined') {
            (window as any).getTimeUpdaterStats = () => {
              return smartTimeUpdater ? smartTimeUpdater.getStats() : null;
            };
            console.log('[SmartTimeUpdater] Debug function available: window.getTimeUpdaterStats()');
          }
        }
      },

      stopTimeUpdater: () => {
        if (smartTimeUpdater) {
          smartTimeUpdater.stop();
          smartTimeUpdater = null;
        }
      },

      getTimeUpdaterStats: () => {
        return smartTimeUpdater ? smartTimeUpdater.getStats() : null;
      },

      // 初始化操作
      initializeApp: () => {
        const state = get();

        // 只在客户端环境中执行时间相关的初始化
        if (typeof window !== 'undefined') {
          // 延迟一帧确保hydration完成，避免hydration mismatch
          requestAnimationFrame(() => {
            // 立即更新正确的时间状态，覆盖SSR期间的默认值
            get().updateCurrentTime();
          });
        }

        // 如果没有活动，创建默认活动
        if (state.activities.length === 0) {
          set({ activities: createDefaultActivities() });
        }

        // 确保当前日期的时间块存在
        const currentDate = state.currentDate;
        if (!state.timeBlocks[currentDate]) {
          set(state => ({
            timeBlocks: {
              ...state.timeBlocks,
              [currentDate]: generateTimeBlocks(currentDate)
            }
          }));
        }

        // 设置预加载监听器
        if (typeof window !== 'undefined') {
          const handlePreload = (event: CustomEvent) => {
            const { dates } = event.detail;
            dates.forEach((date: string) => {
              // 预加载时间块
              if (!get().timeBlocks[date]) {
                set(state => ({
                  timeBlocks: {
                    ...state.timeBlocks,
                    [date]: generateTimeBlocks(date)
                  }
                }));
              }

              // 预计算统计数据
              get().getDailyStats(date);
            });
          };

          window.addEventListener('preloadDates', handlePreload as EventListener);
        }

        // 预加载当前日期的相邻日期
        get().preloadAdjacentDates(currentDate);

        // 启动时间更新器
        get().startTimeUpdater();

        // 添加页面卸载时的清理逻辑
        if (typeof window !== 'undefined') {
          const handleBeforeUnload = () => {
            get().stopTimeUpdater();
          };

          window.addEventListener('beforeunload', handleBeforeUnload);
        }
      },

      // 数据管理操作
      setDataManagementDropdown: (show: boolean) => {
        set(state => ({
          dataManagement: {
            ...state.dataManagement,
            showDropdown: show
          }
        }));
      },

      setExportProgress: (progress: number) => {
        set(state => ({
          dataManagement: {
            ...state.dataManagement,
            exportProgress: progress
          }
        }));
      },

      setImportProgress: (progress: number) => {
        set(state => ({
          dataManagement: {
            ...state.dataManagement,
            importProgress: progress
          }
        }));
      },

      setIsExporting: (isExporting: boolean) => {
        set(state => ({
          dataManagement: {
            ...state.dataManagement,
            isExporting
          }
        }));
      },

      setIsImporting: (isImporting: boolean) => {
        set(state => ({
          dataManagement: {
            ...state.dataManagement,
            isImporting
          }
        }));
      },

      setLastImportResult: (result: ImportResult) => {
        set(state => ({
          dataManagement: {
            ...state.dataManagement,
            lastImportResult: result
          }
        }));
      },

      // 导出数据
      exportData: async (format: 'json' | 'csv', dateRange?: { start: string; end: string }) => {
        const state = get();

        try {
          state.setIsExporting(true);
          state.setExportProgress(0);

          // 动态导入导出工具函数
          const { exportJSONFile, exportCSVFile } = await import('@/utils/dataExport');

          state.setExportProgress(50);

          if (format === 'json') {
            exportJSONFile(
              state.activities,
              state.timeBlocks,
              state.dailyStats,
              dateRange
            );
          } else {
            exportCSVFile(
              state.activities,
              state.timeBlocks,
              dateRange
            );
          }

          state.setExportProgress(100);

          // 延迟重置状态，让用户看到完成状态
          setTimeout(() => {
            state.setIsExporting(false);
            state.setExportProgress(0);
          }, 1000);

        } catch (error) {
          console.error('导出失败:', error);
          state.setIsExporting(false);
          state.setExportProgress(0);
          throw error;
        }
      },

      // 导入数据
      importData: async (file: File, strategy: ImportStrategy = ImportStrategy.MERGE) => {
        const state = get();

        try {
          state.setIsImporting(true);
          state.setImportProgress(0);

          // 动态导入导入工具函数
          const { readFileContent, validateImportData, executeImport } = await import('@/utils/dataImport');

          state.setImportProgress(20);

          // 读取文件内容
          const content = await readFileContent(file);
          state.setImportProgress(40);

          // 验证数据格式
          const validation = validateImportData(content);
          if (!validation.isValid || !validation.data) {
            const result: ImportResult = {
              success: false,
              message: '数据格式验证失败: ' + validation.errors.join(', '),
              statistics: {
                activitiesAdded: 0,
                activitiesUpdated: 0,
                timeBlocksAdded: 0,
                timeBlocksUpdated: 0,
                daysImported: 0
              }
            };
            state.setLastImportResult(result);
            return result;
          }

          state.setImportProgress(60);

          // 执行导入
          const importResult = executeImport(
            validation.data,
            state.activities,
            state.timeBlocks,
            state.dailyStats,
            strategy
          );

          state.setImportProgress(80);

          if (importResult.success && (importResult as any).data) {
            // 更新状态
            const newData = (importResult as any).data;
            set({
              activities: newData.activities,
              timeBlocks: newData.timeBlocks,
              dailyStats: newData.dailyStats
            });

            // 清除缓存，强制重新计算统计数据
            statsCache.clear();
            dataPreloader.clearQueue();
          }

          state.setImportProgress(100);
          state.setLastImportResult(importResult);

          // 延迟重置状态
          setTimeout(() => {
            state.setIsImporting(false);
            state.setImportProgress(0);
          }, 1000);

          return importResult;

        } catch (error) {
          console.error('导入失败:', error);
          const result: ImportResult = {
            success: false,
            message: '导入过程中发生错误: ' + (error as Error).message,
            statistics: {
              activitiesAdded: 0,
              activitiesUpdated: 0,
              timeBlocksAdded: 0,
              timeBlocksUpdated: 0,
              daysImported: 0
            }
          };

          state.setIsImporting(false);
          state.setImportProgress(0);
          state.setLastImportResult(result);

          return result;
        }
      }
    }),
    {
      name: 'chronospect-storage',
      // 只持久化核心数据，UI状态和时间状态不持久化
      partialize: (state) => ({
        currentDate: state.currentDate,
        activities: state.activities,
        timeBlocks: state.timeBlocks,
        dailyStats: state.dailyStats,
        // currentTimeSlot 和 currentTime 不持久化，每次启动时重新计算
      }),
      // 使用createJSONStorage确保在浏览器环境中才使用localStorage
      storage: createJSONStorage(() => {
        if (typeof window === 'undefined') {
          // SSR环境下返回一个空的storage
          return {
            getItem: () => null,
            setItem: () => {},
            removeItem: () => {}
          };
        }
        return localStorage;
      })
    }
  )
);
