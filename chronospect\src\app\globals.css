@import "tailwindcss";

/* ===== Chronospect Design System ===== */
/* 基于PRD FR-05极致美学要求的统一设计token系统 */

:root {
  /* === 基础颜色系统 === */
  --background: #ffffff;
  --foreground: #171717;

  /* === 主题色彩 === */
  /* 主色调 - 蓝色系 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bedbff;
  --primary-300: #90c5ff;
  --primary-500: #3080ff;
  --primary-600: #155dfc;
  --primary-700: #1d4ed8;
  --primary-800: #193cb8;
  --primary-900: #1c398e;

  /* 中性色系 */
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5dc;
  --neutral-400: #99a1af;
  --neutral-500: #6a7282;
  --neutral-600: #4a5565;
  --neutral-700: #364153;
  --neutral-800: #1e2939;
  --neutral-900: #101828;

  /* 语义色彩 */
  --success-50: #ecfdf5;
  --success-500: #00c758;
  --success-600: #00a544;

  --warning-50: #fffbeb;
  --warning-500: #fac800;
  --warning-600: #d97706;

  --error-50: #fef2f2;
  --error-500: #fb2c36;
  --error-600: #e40014;

  --info-50: #eef2ff;
  --info-500: #ac4bff;
  --info-600: #9810fa;

  /* === 时间段主题色彩 === */
  /* 深夜 (00-04) - 深蓝紫色调 */
  --time-segment-night-bg: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
  --time-segment-night-border: #4338ca;
  --time-segment-night-text: #c7d2fe;

  /* 黎明 (04-08) - 温暖橙粉色调 */
  --time-segment-dawn-bg: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  --time-segment-dawn-border: #f59e0b;
  --time-segment-dawn-text: #92400e;

  /* 上午 (08-12) - 清新蓝绿色调 */
  --time-segment-morning-bg: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  --time-segment-morning-border: #10b981;
  --time-segment-morning-text: #065f46;

  /* 下午 (12-16) - 明亮黄色调 */
  --time-segment-afternoon-bg: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  --time-segment-afternoon-border: #eab308;
  --time-segment-afternoon-text: #a16207;

  /* 傍晚 (16-20) - 温暖橙红色调 */
  --time-segment-evening-bg: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  --time-segment-evening-border: #ea580c;
  --time-segment-evening-text: #c2410c;

  /* 夜晚 (20-24) - 深紫蓝色调 */
  --time-segment-twilight-bg: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  --time-segment-twilight-border: #6b7280;
  --time-segment-twilight-text: #374151;

  /* === 间距系统 === */
  --spacing-0: 0;
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */

  /* === 圆角系统 === */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* === 阴影系统 === */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* === 字体系统 === */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* === 动画系统 === */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* === 时间轴流式布局专用间距 === */
  --time-grid-gap: var(--spacing-2);
  --time-segment-gap: var(--spacing-3);
  --time-block-height: 60px;

  /* === 智能拖拽系统样式 === */
  --drag-preview-z-index: 20;
  --drag-path-z-index: 5;
  --drag-guide-z-index: 50;
  --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* === 断点系统 === */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

/* === 全局样式 === */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* === 统一组件样式 === */
/* 卡片组件基础样式 */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--neutral-200);
  padding: var(--spacing-6);
  transition: all var(--duration-normal) var(--ease-out);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  border-color: var(--neutral-300);
}

/* 按钮组件基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-fast) var(--ease-out);
  cursor: pointer;
  border: none;
  outline: none;
}

.btn-primary {
  background: var(--primary-500);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--neutral-100);
  color: var(--neutral-700);
}

.btn-secondary:hover {
  background: var(--neutral-200);
  color: var(--neutral-800);
}

/* 输入框组件基础样式 */
.input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  transition: all var(--duration-fast) var(--ease-out);
  background: white;
}

.input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(48 128 255 / 0.1);
}

/* 动画工具类 */
.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-spring);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
