'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Save, X, Palette, Image } from 'lucide-react';
import * as LucideIcons from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import { Activity } from '@/types';
import { ColorPicker } from './ColorPicker';
import { IconSelector } from './IconSelector';

interface ActivityFormProps {
  activity?: Activity | null;
  onSave: () => void;
  onCancel: () => void;
}

interface FormData {
  name: string;
  color: string;
  icon: string;
  description: string;
}

interface FormErrors {
  name?: string;
  color?: string;
  icon?: string;
}

export function ActivityForm({ activity, onSave, onCancel }: ActivityFormProps) {
  const { addActivity, updateActivity } = useAppStore();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    color: '#3B82F6',
    icon: 'Circle',
    description: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showIconSelector, setShowIconSelector] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (activity) {
      setFormData({
        name: activity.name,
        color: activity.color,
        icon: activity.icon || 'Circle',
        description: activity.description || ''
      });
    } else {
      // 重置为默认值
      setFormData({
        name: '',
        color: '#3B82F6',
        icon: 'Circle',
        description: ''
      });
    }
    setErrors({});
    setShowColorPicker(false);
    setShowIconSelector(false);
  }, [activity]);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+S 或 Command+S 保存表单
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        const form = document.querySelector('form');
        if (form) {
          form.requestSubmit();
        }
        return;
      }

      // ESC键关闭颜色选择器和图标选择器
      if (event.key === 'Escape') {
        if (showColorPicker) {
          setShowColorPicker(false);
          return;
        }
        if (showIconSelector) {
          setShowIconSelector(false);
          return;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showColorPicker, showIconSelector]);

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = '活动名称不能为空';
    } else if (formData.name.trim().length > 20) {
      newErrors.name = '活动名称不能超过20个字符';
    }

    if (!formData.color) {
      newErrors.color = '请选择活动颜色';
    }

    if (!formData.icon) {
      newErrors.icon = '请选择活动图标';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const activityData = {
        name: formData.name.trim(),
        color: formData.color,
        icon: formData.icon,
        description: formData.description.trim() || undefined
      };

      if (activity) {
        // 更新现有活动
        updateActivity(activity.id, activityData);
      } else {
        // 创建新活动
        addActivity(activityData);
      }

      // 延迟一下以显示提交状态
      await new Promise(resolve => setTimeout(resolve, 300));
      onSave();
    } catch (error) {
      console.error('保存活动失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除相关错误
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // 处理颜色选择
  const handleColorSelect = (color: string) => {
    handleInputChange('color', color);
    setShowColorPicker(false);
  };

  // 处理图标选择
  const handleIconSelect = (icon: string) => {
    handleInputChange('icon', icon);
    setShowIconSelector(false);
  };

  // 渲染图标组件
  const renderIcon = (iconName: string, size: number = 20) => {
    const IconComponent = (LucideIcons as any)[iconName];
    if (!IconComponent) {
      return <div className="w-5 h-5 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">?</div>;
    }
    return <IconComponent size={size} />;
  };

  return (
    <div className="p-8">
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 活动名称 */}
        <div className="space-y-3">
          <label htmlFor="name" className="block text-base font-medium text-gray-700">
            活动名称 <span className="text-red-500">*</span>
          </label>
          <input
            id="name"
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-4 py-3 text-base border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="请输入活动名称"
            maxLength={20}
          />
          {errors.name && (
            <p className="text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* 活动描述 */}
        <div className="space-y-3">
          <label htmlFor="description" className="block text-base font-medium text-gray-700">
            活动描述
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            className="w-full px-4 py-3 text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
            placeholder="请输入活动描述（可选）"
            rows={4}
            maxLength={100}
          />
          <div className="text-sm text-gray-500 text-right">
            {formData.description.length}/100
          </div>
        </div>

        {/* 颜色选择 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="block text-base font-medium text-gray-700">
              活动颜色 <span className="text-red-500">*</span>
            </label>
            <button
              type="button"
              onClick={() => setShowColorPicker(!showColorPicker)}
              className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <Palette className="w-4 h-4" />
              <span>{showColorPicker ? '收起' : '选择颜色'}</span>
            </button>
          </div>

          {/* 当前选中的颜色显示 */}
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div
              className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
              style={{ backgroundColor: formData.color }}
            />
            <div>
              <div className="text-sm font-medium text-gray-900">当前颜色</div>
              <div className="text-xs text-gray-500">{formData.color}</div>
            </div>
          </div>

          {/* 内联颜色选择器 */}
          {showColorPicker && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="border border-gray-200 rounded-lg p-4 bg-white"
            >
              <ColorPicker
                selectedColor={formData.color}
                onColorSelect={handleColorSelect}
                onClose={() => setShowColorPicker(false)}
              />
            </motion.div>
          )}

          {errors.color && (
            <p className="text-sm text-red-600">{errors.color}</p>
          )}
        </div>

        {/* 图标选择 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <label className="block text-base font-medium text-gray-700">
              活动图标 <span className="text-red-500">*</span>
            </label>
            <button
              type="button"
              onClick={() => setShowIconSelector(!showIconSelector)}
              className="flex items-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <Image className="w-4 h-4" />
              <span>{showIconSelector ? '收起' : '选择图标'}</span>
            </button>
          </div>

          {/* 当前选中的图标显示 */}
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 flex items-center justify-center text-gray-600 bg-white rounded-lg border border-gray-200">
              {renderIcon(formData.icon, 24)}
            </div>
            <div>
              <div className="text-sm font-medium text-gray-900">当前图标</div>
              <div className="text-xs text-gray-500">{formData.icon}</div>
            </div>
          </div>

          {/* 内联图标选择器 */}
          {showIconSelector && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
              className="border border-gray-200 rounded-lg p-4 bg-white"
            >
              <IconSelector
                selectedIcon={formData.icon}
                onIconSelect={handleIconSelect}
                onClose={() => setShowIconSelector(false)}
              />
            </motion.div>
          )}

          {errors.icon && (
            <p className="text-sm text-red-600">{errors.icon}</p>
          )}
        </div>

        {/* 表单按钮 */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <div className="flex-1 text-sm text-gray-500">
            <kbd className="px-2 py-1 bg-gray-100 border border-gray-300 rounded-md text-gray-600 font-mono">Ctrl+S</kbd>
            <span className="ml-2">保存</span>
          </div>

          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors font-medium"
            disabled={isSubmitting}
          >
            取消
          </button>

          <motion.button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
            whileHover={!isSubmitting ? { scale: 1.02 } : {}}
            whileTap={!isSubmitting ? { scale: 0.98 } : {}}
          >
            <Save className="w-4 h-4" />
            <span>{isSubmitting ? '保存中...' : (activity ? '更新活动' : '创建活动')}</span>
          </motion.button>
        </div>
      </form>
    </div>
  );
}
