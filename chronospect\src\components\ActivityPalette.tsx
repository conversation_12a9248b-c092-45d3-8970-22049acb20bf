'use client';

import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Trash2, Eraser } from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import * as LucideIcons from 'lucide-react';

interface ActivityPaletteProps {
  onActivitySelect: (activityId: string | null) => void;
  onClose: () => void;
  selectedBlocks: number[];
}

export function ActivityPalette({ onActivitySelect, onClose, selectedBlocks }: ActivityPaletteProps) {
  const { activities } = useAppStore();

  // 获取图标组件
  const getIconComponent = (iconName?: string) => {
    if (!iconName) return null;

    const IconComponent = (LucideIcons as any)[iconName];
    return IconComponent ? <IconComponent size={18} /> : null;
  };

  // 处理活动选择
  const handleActivityClick = (activityId: string) => {
    onActivitySelect(activityId);
  };

  // 处理清除选择
  const handleClearClick = () => {
    onActivitySelect(null);
  };

  // 阻止事件冒泡
  const handlePaletteClick = (event: React.MouseEvent) => {
    event.stopPropagation();
  };

  // 添加ESC键监听和点击外部关闭
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      // 检查点击是否在工具栏外部
      if (!target.closest('[data-activity-palette]')) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  return (
    <AnimatePresence>
      {/* 极简水平工具栏 - 定位在时间网格下方 */}
      <motion.div
        className="absolute left-0 right-0 z-50 mx-auto max-w-4xl px-4"
        style={{ top: '100%', marginTop: '20px' }} // 定位在时间网格下方
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 20, opacity: 0 }}
        transition={{ type: "spring", stiffness: 400, damping: 25 }}
        onClick={handlePaletteClick}
        data-activity-palette
      >
        {/* 轻量化工具栏背景 */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-3">
          {/* 轻量化活动选择器 */}
          <div className="flex flex-wrap items-center justify-center gap-3">
            {/* 活动列表 */}
            {activities.map((activity) => (
              <motion.button
                key={activity.id}
                onClick={() => handleActivityClick(activity.id)}
                className="flex items-center gap-2 px-4 py-2.5 bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300 rounded-lg transition-all duration-200 group shadow-sm hover:shadow-md"
                whileHover={{ scale: 1.02, y: -1 }}
                whileTap={{ scale: 0.98 }}
              >
                {/* 颜色圆点 */}
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0 ring-1 ring-white"
                  style={{ backgroundColor: activity.color }}
                />

                {/* 活动名称 */}
                <span className="text-sm font-medium text-gray-700 group-hover:text-gray-900 whitespace-nowrap">
                  {activity.name}
                </span>
              </motion.button>
            ))}



            {/* 简化清除按钮 */}
            <motion.button
              onClick={handleClearClick}
              className="flex items-center justify-center w-10 h-10 bg-gray-50 hover:bg-red-50 border border-gray-200 hover:border-red-300 rounded-lg transition-all duration-200 group shadow-sm hover:shadow-md"
              whileHover={{ scale: 1.02, y: -1 }}
              whileTap={{ scale: 0.98 }}
              title="清除选择"
            >
              <Eraser size={16} className="text-gray-500 group-hover:text-red-500" />
            </motion.button>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
