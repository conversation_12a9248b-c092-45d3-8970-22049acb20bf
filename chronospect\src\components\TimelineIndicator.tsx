'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';
import { formatTime } from '@/utils/timeUtils';
import { useIsClient } from '@/hooks/useIsClient';

export function TimelineIndicator() {
  const { currentDate, currentTimeSlot, currentTime } = useAppStore();
  const isClient = useIsClient();

  // 只在客户端渲染，避免SSR hydration mismatch
  if (!isClient) {
    return null;
  }

  // 只在当天显示时间线指示器
  const isToday = currentDate === currentTime.date;
  
  if (!isToday) {
    return null;
  }
  
  // 计算当前时间在网格中的精确位置
  const calculateTimelinePosition = () => {
    const { hour, minute } = currentTime;

    // 计算当前时间对应的timeSlot (0-47)
    const currentTimeSlot = hour * 2 + (minute >= 30 ? 1 : 0);

    // 统一坐标系统：6列×8行，按列优先布局
    // 第1列：00:00-04:00 (timeSlot 0-7), 第2列：04:00-08:00 (timeSlot 8-15)
    // 第3列：08:00-12:00 (timeSlot 16-23), 第4列：12:00-16:00 (timeSlot 24-31)
    // 第5列：16:00-20:00 (timeSlot 32-39), 第6列：20:00-24:00 (timeSlot 40-47)
    // timeSlot转行列：row = timeSlot % 8, col = Math.floor(timeSlot / 8)
    const row = currentTimeSlot % 8;
    const column = Math.floor(currentTimeSlot / 8);

    // 计算在当前时间槽内的精确位置 (0-1)
    const minutesInSlot = minute % 30; // 在30分钟时间槽内的分钟数
    const positionInSlot = minutesInSlot / 30; // 在时间槽内的位置比例

    return {
      row: Math.min(row, 7), // 确保不超出边界 (0-7)
      column: Math.min(column, 5), // 确保不超出边界 (0-5)
      positionInSlot: Math.min(positionInSlot, 1) // 在时间槽内的位置 (0-1)
    };
  };
  
  const position = calculateTimelinePosition();
  
  // 计算时间线的CSS位置
  const getTimelineStyle = () => {
    // 网格间距 - 更新为新的间距值
    const gap = 8; // var(--spacing-2) = 8px

    // 每个区块的宽度和高度 - 适配6列布局
    const blockWidth = `calc((100% - ${gap * 5}px) / 6)`;
    const blockHeight = 60; // 60px

    // 计算左边距：到达指定列 + 列内的精确位置
    const leftOffset = `calc(${position.column} * (${blockWidth} + ${gap}px) + ${position.positionInSlot} * ${blockWidth})`;

    // 计算顶部位置：行位置 * (区块高度 + 间距)
    const topOffset = position.row * (blockHeight + gap);

    return {
      left: leftOffset,
      top: `${topOffset}px`,
      height: `${blockHeight}px`
    };
  };
  
  const timelineStyle = getTimelineStyle();
  const currentTimeString = `${currentTime.hour.toString().padStart(2, '0')}:${currentTime.minute.toString().padStart(2, '0')}`;
  
  return (
    <motion.div
      className="absolute pointer-events-none"
      style={{
        ...timelineStyle,
        width: '2px',
        backgroundColor: 'var(--primary-500)',
        borderRadius: '1px',
        boxShadow: '0 0 8px rgba(59, 130, 246, 0.5)',
        zIndex: 10
      }}
      initial={{ opacity: 0, scaleY: 0 }}
      animate={{ opacity: 1, scaleY: 1 }}
      transition={{
        duration: 0.5,
        ease: [0.4, 0, 0.2, 1]
      }}
    >
      {/* 时间标签 */}
      <motion.div
        className="absolute"
        style={{
          top: '-28px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'var(--primary-500)',
          color: 'white',
          fontSize: 'var(--font-size-xs)',
          fontWeight: 'var(--font-weight-medium)',
          padding: 'var(--spacing-1) var(--spacing-2)',
          borderRadius: 'var(--radius-md)',
          boxShadow: 'var(--shadow-md)',
          whiteSpace: 'nowrap'
        }}
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.3,
          delay: 0.2,
          ease: [0.4, 0, 0.2, 1]
        }}
      >
        {currentTimeString}
      </motion.div>
      
      {/* 顶部指示点 */}
      <motion.div
        className="absolute"
        style={{
          top: '-4px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '8px',
          height: '8px',
          backgroundColor: 'var(--primary-500)',
          borderRadius: '50%',
          border: '2px solid white',
          boxShadow: 'var(--shadow-sm)'
        }}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
          duration: 0.3,
          delay: 0.1,
          ease: [0.68, -0.55, 0.265, 1.55]
        }}
      />
      
      {/* 底部指示点 */}
      <motion.div
        className="absolute"
        style={{
          bottom: '-4px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '8px',
          height: '8px',
          backgroundColor: 'var(--primary-500)',
          borderRadius: '50%',
          border: '2px solid white',
          boxShadow: 'var(--shadow-sm)'
        }}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{
          duration: 0.3,
          delay: 0.15,
          ease: [0.68, -0.55, 0.265, 1.55]
        }}
      />
    </motion.div>
  );
}
