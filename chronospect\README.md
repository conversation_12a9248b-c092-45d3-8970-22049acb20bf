# Chronospect (时间洞察)

> 专注于回顾性时间分析的Web应用程序，通过数据富集网格帮助您理解时间的真实去向

## 🌟 核心特性

### 📊 48区块数据富集网格
- **12x4网格布局**: 精确代表一天24小时中的48个30分钟时间区块
- **直观时间标识**: 每个区块清晰标注对应的时间范围（如"08:00 - 08:30"）
- **零摩擦记录**: 单击即可快速填充时间块，最多2次点击完成记录

### 🎨 用户自定义活动库
- **个性化活动**: 支持创建、编辑和删除自定义活动类别
- **视觉区分**: 每个活动可关联独特的颜色和图标
- **预设活动**: 内置8种常用活动（深度工作、会议、学习、运动等）

### 🖱️ 流畅交互体验
- **单击填充**: 点击时间块弹出活动选择面板
- **批量操作**: 拖拽选择多个连续时间块（开发中）
- **即时反馈**: 流畅的动画和视觉反馈
- **智能提示**: Hover显示活动详情和时间信息

### 🎯 极致美学设计
- **现代UI**: 极简主义设计，干净整洁的界面
- **精心调色**: 和谐且具吸引力的主题调色板
- **优雅动画**: 微妙精致的过渡动画，提供愉悦的掌控感
- **响应式**: 完美适配不同屏幕尺寸

## 🚀 工作原理

Chronospect采用回顾性时间分析方法，摒弃传统规划工具"预测未来"的模式：

1. **数据记录**: 通过48区块网格记录实际的时间消耗
2. **活动分类**: 使用个性化活动库对时间进行分类
3. **视觉反馈**: 通过颜色和图标直观展示时间分布
4. **数据洞察**: 帮助用户客观认识时间的真实去向

## 🛠️ 技术栈

- **框架**: Next.js 15 + TypeScript + App Router
- **样式**: Tailwind CSS 4 + Framer Motion
- **状态管理**: Zustand
- **图标**: Lucide React
- **数据存储**: LocalStorage (MVP) + 计划支持云端同步
- **部署**: Vercel

## 📦 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn 或 pnpm

### 安装步骤

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 打开浏览器访问
open http://localhost:3000
```

### 构建部署

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🎮 使用指南

### 基础操作
1. **选择日期**: 使用顶部日期导航器切换查看不同日期
2. **记录活动**: 点击任意时间块，从弹出的活动面板中选择对应活动
3. **查看详情**: 将鼠标悬停在已填充的时间块上查看活动详情
4. **清除记录**: 在活动选择面板中点击"清除"按钮

### 高级功能
- **批量填充**: 拖拽选择多个时间块进行批量操作 ✅
- **活动管理**: 创建和管理自定义活动类别 ✅
- **数据复盘**: 查看时间分布统计和分析报告 ✅
- **数据可视化**: 饼图和条形图展示活动分布 ✅
- **智能洞察**: 时间利用率分析和活动排行榜 ✅
- **跨日期对比**: 任意日期间的数据对比分析 ✅
- **智能缓存**: LRU缓存和数据预加载优化 ✅
- **加载优化**: 骨架屏和异步加载体验 ✅
- **数据导入导出**: 完整的JSON/CSV数据导出功能 ✅

## ❓ 常见问题 (QA)

### Q: 数据会丢失吗？
A: 目前数据保存在浏览器的LocalStorage中，不会因为刷新页面而丢失。未来版本将支持云端同步。

### Q: 支持移动端吗？
A: 当前版本主要针对桌面端优化，移动端支持在后续版本中完善。

### Q: 可以导出数据吗？
A: ✅ 已支持完整的数据导出功能！支持JSON和CSV两种格式，包含所有时间块数据（包括空闲时间）和完整的活动信息。点击顶部"数据管理"按钮即可导出。

### Q: 如何自定义活动？
A: 点击活动面板右上角的设置按钮（齿轮图标），即可进入活动管理界面。支持创建、编辑、删除自定义活动，以及修改活动的颜色和图标。

### Q: 数据导出包含哪些信息？
A: 数据导出功能提供两种格式：
- **JSON格式**: 包含完整的结构化数据，包括元数据、活动定义、时间块数据等
- **CSV格式**: 包含11个字段的详细时间块信息：日期、时间槽、开始时间、结束时间、活动ID、活动名称、活动颜色、活动描述、活动图标、持续时间、状态

### Q: 导出的数据包含空闲时间吗？
A: 是的！导出功能会包含所有时间块，包括未分配活动的空闲时间。空闲时间在CSV中标记为"空闲时间"状态，在JSON中`activityId`为null。

## 📈 更新历史

### v0.1.0 (2025-07-18)
- ✅ 实现48区块数据富集网格
- ✅ 完成用户自定义活动库基础功能
- ✅ 实现单击区块填充交互
- ✅ 添加日期导航功能
- ✅ 完成极致美学UI设计
- ✅ 集成Framer Motion动画系统
- ✅ 实现LocalStorage数据持久化

### v0.2.0 (2025-07-18)
- ✅ 实现每日复盘仪表盘
- ✅ 集成Chart.js数据可视化
- ✅ 添加饼图和条形图展示
- ✅ 完成时间利用率分析
- ✅ 实现活动排行榜功能
- ✅ 添加智能洞察和连续性评分
- ✅ 完成视图切换功能

### v0.3.0 (2025-07-18)
- ✅ 实现LRU缓存算法优化数据加载性能
- ✅ 添加智能数据预加载机制
- ✅ 完成骨架屏和加载状态指示器系统
- ✅ 实现跨日期数据对比分析功能
- ✅ 集成内存监控和性能优化
- ✅ 优化复盘视图的异步加载体验

### v0.3.0 (2025-07-21)
- ✅ 完成活动管理CRUD功能开发
- ✅ 实现活动创建、编辑、删除界面
- ✅ 添加颜色选择器和图标选择器
- ✅ 实现活动分组管理（预设vs自定义）
- ✅ 集成键盘快捷键支持
- ✅ 添加活动搜索和筛选功能
- ✅ 修复SSR Hydration错误
- ✅ 优化组件Props传递机制
- ✅ 提升整体用户体验和稳定性

### v0.4.0 (2025-07-28)
- ✅ 完成数据导入导出功能开发
- ✅ 实现JSON格式完整数据导出
- ✅ 实现CSV格式详细时间块导出
- ✅ 修复CSV导出数据不完整问题
- ✅ 修复JSON导出日期范围计算错误
- ✅ 添加空闲时间块的完整记录
- ✅ 扩展CSV导出字段至11个详细字段
- ✅ 实现智能数据处理和状态标识
- ✅ 添加导出功能测试面板（开发环境）
- ✅ 优化用户体验和错误处理机制

### 计划中的功能
- 📱 移动端适配优化
- 🌙 深色/浅色主题切换
- ☁️ 云端数据同步
- 🔔 智能提醒和通知
- 📥 数据导入功能

---

**Chronospect** - 让时间变得可见，让洞察成为可能 ✨
