'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { useAppStore } from '@/stores/useAppStore';
import { formatDate, getAdjacentDate } from '@/utils/timeUtils';
import { calculateDailyStats } from '@/utils/statsUtils';
import { DailyStats } from '@/types';
import { CustomDatePicker } from './CustomDatePicker';

interface DateComparisonProps {
  currentDate: string;
}

export function DateComparison({ currentDate }: DateComparisonProps) {
  const {
    getDailyStatsSync,
    getDailyStatsAsync,
    getTimeBlocksForDate,
    activities
  } = useAppStore();

  const [compareDate, setCompareDate] = useState(() =>
    getAdjacentDate(currentDate, -1) // 默认对比昨天
  );
  const [isExpanded, setIsExpanded] = useState(false);

  // 预加载数据 - 在useEffect中进行，避免渲染中的副作用
  useEffect(() => {
    getDailyStatsAsync(currentDate);
    getDailyStatsAsync(compareDate);
  }, [currentDate, compareDate, getDailyStatsAsync]);

  // 安全获取统计数据 - 使用useMemo缓存计算结果
  const currentStats = useMemo(() => {
    // 首先尝试从缓存获取
    const cached = getDailyStatsSync(currentDate);
    if (cached) return cached;

    // 如果没有缓存，进行本地计算但不更新全局状态
    const timeBlocks = getTimeBlocksForDate(currentDate);
    return calculateDailyStats(timeBlocks, activities);
  }, [currentDate, getDailyStatsSync, getTimeBlocksForDate, activities]);

  const compareStats = useMemo(() => {
    // 首先尝试从缓存获取
    const cached = getDailyStatsSync(compareDate);
    if (cached) return cached;

    // 如果没有缓存，进行本地计算但不更新全局状态
    const timeBlocks = getTimeBlocksForDate(compareDate);
    return calculateDailyStats(timeBlocks, activities);
  }, [compareDate, getDailyStatsSync, getTimeBlocksForDate, activities]);

  // 计算对比指标
  const comparison = useMemo(() => {
    const filledMinutesDiff = currentStats.filledMinutes - compareStats.filledMinutes;
    const filledPercentageDiff = currentStats.filledPercentage - compareStats.filledPercentage;
    const activitiesDiff = currentStats.activities.length - compareStats.activities.length;

    return {
      filledMinutes: {
        value: filledMinutesDiff,
        percentage: compareStats.filledMinutes > 0 
          ? (filledMinutesDiff / compareStats.filledMinutes) * 100 
          : 0,
        trend: filledMinutesDiff > 0 ? 'up' : filledMinutesDiff < 0 ? 'down' : 'same'
      },
      filledPercentage: {
        value: filledPercentageDiff,
        trend: filledPercentageDiff > 0 ? 'up' : filledPercentageDiff < 0 ? 'down' : 'same'
      },
      activities: {
        value: activitiesDiff,
        trend: activitiesDiff > 0 ? 'up' : activitiesDiff < 0 ? 'down' : 'same'
      }
    };
  }, [currentStats, compareStats]);

  // 获取趋势图标
  const getTrendIcon = (trend: 'up' | 'down' | 'same') => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={16} className="text-green-500" />;
      case 'down':
        return <TrendingDown size={16} className="text-red-500" />;
      default:
        return <Minus size={16} className="text-gray-500" />;
    }
  };

  // 获取趋势颜色
  const getTrendColor = (trend: 'up' | 'down' | 'same') => {
    switch (trend) {
      case 'up':
        return 'var(--success-600)';
      case 'down':
        return 'var(--error-600)';
      default:
        return 'var(--neutral-500)';
    }
  };

  return (
    <motion.div
      className="card"
      style={{
        background: 'white',
        borderRadius: 'var(--radius-xl)',
        boxShadow: 'var(--shadow-lg)',
        border: '1px solid var(--neutral-200)',
        padding: 'var(--spacing-6)'
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
      whileHover={{
        boxShadow: 'var(--shadow-xl)',
        borderColor: 'var(--neutral-300)'
      }}
    >
      {/* 标题和展开按钮 */}
      <div className="flex items-center justify-between" style={{ marginBottom: 'var(--spacing-4)' }}>
        <h3 style={{
          fontSize: 'var(--font-size-lg)',
          fontWeight: 'var(--font-weight-semibold)',
          color: 'var(--neutral-900)',
          display: 'flex',
          alignItems: 'center'
        }}>
          📊 日期对比分析
        </h3>
        <motion.button
          onClick={() => setIsExpanded(!isExpanded)}
          style={{
            color: 'var(--primary-500)',
            fontSize: 'var(--font-size-sm)',
            fontWeight: 'var(--font-weight-medium)',
            background: 'none',
            border: 'none',
            cursor: 'pointer'
          }}
          whileHover={{
            scale: 1.05,
            color: 'var(--primary-600)'
          }}
          whileTap={{ scale: 0.95 }}
        >
          {isExpanded ? '收起' : '展开详情'}
        </motion.button>
      </div>

      {/* 美观的日期选择器 */}
      <div className="flex items-center" style={{
        gap: 'var(--spacing-4)',
        marginBottom: 'var(--spacing-6)',
        flexWrap: 'wrap'
      }}>
        <div className="flex items-center" style={{ gap: 'var(--spacing-2)' }}>
          <span style={{
            fontSize: 'var(--font-size-sm)',
            color: 'var(--neutral-600)',
            fontWeight: 'var(--font-weight-medium)'
          }}>
            对比日期:
          </span>
        </div>
        <CustomDatePicker
          value={compareDate}
          onChange={setCompareDate}
          className="flex-shrink-0"
        />
        <span style={{
          fontSize: 'var(--font-size-sm)',
          color: 'var(--neutral-500)',
          fontWeight: 'var(--font-weight-medium)'
        }}>
          vs {formatDate(currentDate)}
        </span>
      </div>

      {/* 核心对比指标 */}
      <div className="grid grid-cols-1 md:grid-cols-3" style={{
        gap: 'var(--spacing-4)',
        marginBottom: 'var(--spacing-6)'
      }}>
        {/* 记录时间对比 */}
        <motion.div
          style={{
            background: 'var(--neutral-50)',
            borderRadius: 'var(--radius-lg)',
            padding: 'var(--spacing-4)',
            border: '1px solid var(--neutral-200)'
          }}
          whileHover={{
            background: 'var(--neutral-100)',
            borderColor: 'var(--neutral-300)'
          }}
          transition={{ duration: 0.15 }}
        >
          <div className="flex items-center justify-between" style={{ marginBottom: 'var(--spacing-2)' }}>
            <span style={{
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)',
              color: 'var(--neutral-700)'
            }}>
              记录时间
            </span>
            {getTrendIcon(comparison.filledMinutes.trend)}
          </div>
          <div style={{
            fontSize: 'var(--font-size-lg)',
            fontWeight: 'var(--font-weight-semibold)',
            color: 'var(--neutral-900)'
          }}>
            {Math.abs(comparison.filledMinutes.value)} 分钟
          </div>
          <div style={{
            fontSize: 'var(--font-size-sm)',
            color: getTrendColor(comparison.filledMinutes.trend)
          }}>
            {comparison.filledMinutes.trend === 'up' ? '+' : comparison.filledMinutes.trend === 'down' ? '-' : ''}
            {Math.abs(comparison.filledMinutes.percentage).toFixed(1)}%
          </div>
        </motion.div>

        {/* 时间利用率对比 */}
        <motion.div
          style={{
            background: 'var(--neutral-50)',
            borderRadius: 'var(--radius-lg)',
            padding: 'var(--spacing-4)',
            border: '1px solid var(--neutral-200)'
          }}
          whileHover={{
            background: 'var(--neutral-100)',
            borderColor: 'var(--neutral-300)'
          }}
          transition={{ duration: 0.15 }}
        >
          <div className="flex items-center justify-between" style={{ marginBottom: 'var(--spacing-2)' }}>
            <span style={{
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)',
              color: 'var(--neutral-700)'
            }}>
              时间利用率
            </span>
            {getTrendIcon(comparison.filledPercentage.trend)}
          </div>
          <div style={{
            fontSize: 'var(--font-size-lg)',
            fontWeight: 'var(--font-weight-semibold)',
            color: 'var(--neutral-900)'
          }}>
            {Math.abs(comparison.filledPercentage.value).toFixed(1)}%
          </div>
          <div style={{
            fontSize: 'var(--font-size-sm)',
            color: getTrendColor(comparison.filledPercentage.trend)
          }}>
            {comparison.filledPercentage.trend === 'up' ? '提升' : comparison.filledPercentage.trend === 'down' ? '下降' : '持平'}
          </div>
        </motion.div>

        {/* 活动类型对比 */}
        <motion.div
          style={{
            background: 'var(--neutral-50)',
            borderRadius: 'var(--radius-lg)',
            padding: 'var(--spacing-4)',
            border: '1px solid var(--neutral-200)'
          }}
          whileHover={{
            background: 'var(--neutral-100)',
            borderColor: 'var(--neutral-300)'
          }}
          transition={{ duration: 0.15 }}
        >
          <div className="flex items-center justify-between" style={{ marginBottom: 'var(--spacing-2)' }}>
            <span style={{
              fontSize: 'var(--font-size-sm)',
              fontWeight: 'var(--font-weight-medium)',
              color: 'var(--neutral-700)'
            }}>
              活动类型
            </span>
            {getTrendIcon(comparison.activities.trend)}
          </div>
          <div style={{
            fontSize: 'var(--font-size-lg)',
            fontWeight: 'var(--font-weight-semibold)',
            color: 'var(--neutral-900)'
          }}>
            {Math.abs(comparison.activities.value)} 种
          </div>
          <div style={{
            fontSize: 'var(--font-size-sm)',
            color: getTrendColor(comparison.activities.trend)
          }}>
            {comparison.activities.trend === 'up' ? '增加' : comparison.activities.trend === 'down' ? '减少' : '相同'}
          </div>
        </motion.div>
      </div>

      {/* 展开的详细对比 */}
      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="border-t border-gray-200 pt-6"
        >
          {/* 活动对比表格 */}
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">活动详细对比</h4>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-2 text-gray-600">活动</th>
                    <th className="text-right py-2 text-gray-600">{formatDate(compareDate)}</th>
                    <th className="text-right py-2 text-gray-600">{formatDate(currentDate)}</th>
                    <th className="text-right py-2 text-gray-600">变化</th>
                  </tr>
                </thead>
                <tbody>
                  {getActivityComparison(currentStats, compareStats).map((item, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-2 flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: item.color }}
                        />
                        <span>{item.name}</span>
                      </td>
                      <td className="text-right py-2 text-gray-600">
                        {item.compareMinutes} 分钟
                      </td>
                      <td className="text-right py-2 text-gray-900 font-medium">
                        {item.currentMinutes} 分钟
                      </td>
                      <td className={`text-right py-2 ${getTrendColor(item.trend)}`}>
                        {item.trend === 'up' ? '+' : item.trend === 'down' ? '-' : ''}
                        {Math.abs(item.diff)} 分钟
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* 洞察总结 */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="text-md font-medium text-blue-900 mb-2">📝 对比洞察</h4>
            <p className="text-sm text-blue-800">
              {generateComparisonInsight(comparison, formatDate(compareDate), formatDate(currentDate))}
            </p>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

// 获取活动对比数据
function getActivityComparison(currentStats: DailyStats, compareStats: DailyStats) {
  const allActivities = new Map<string, {
    name: string;
    color: string;
    currentMinutes: number;
    compareMinutes: number;
  }>();

  // 添加当前日期的活动
  currentStats.activities.forEach(activity => {
    allActivities.set(activity.activityId, {
      name: activity.activityName,
      color: activity.color,
      currentMinutes: activity.totalMinutes,
      compareMinutes: 0
    });
  });

  // 添加对比日期的活动
  compareStats.activities.forEach(activity => {
    const existing = allActivities.get(activity.activityId);
    if (existing) {
      existing.compareMinutes = activity.totalMinutes;
    } else {
      allActivities.set(activity.activityId, {
        name: activity.activityName,
        color: activity.color,
        currentMinutes: 0,
        compareMinutes: activity.totalMinutes
      });
    }
  });

  return Array.from(allActivities.values()).map(activity => {
    const diff = activity.currentMinutes - activity.compareMinutes;
    return {
      ...activity,
      diff,
      trend: diff > 0 ? 'up' as const : diff < 0 ? 'down' as const : 'same' as const
    };
  }).sort((a, b) => Math.abs(b.diff) - Math.abs(a.diff));
}

// 生成对比洞察
function generateComparisonInsight(
  comparison: any, 
  compareDate: string, 
  currentDate: string
): string {
  const insights: string[] = [];

  if (comparison.filledMinutes.trend === 'up') {
    insights.push(`相比${compareDate}，${currentDate}的时间记录增加了${comparison.filledMinutes.value}分钟`);
  } else if (comparison.filledMinutes.trend === 'down') {
    insights.push(`相比${compareDate}，${currentDate}的时间记录减少了${Math.abs(comparison.filledMinutes.value)}分钟`);
  }

  if (comparison.filledPercentage.trend === 'up') {
    insights.push(`时间利用率提升了${comparison.filledPercentage.value.toFixed(1)}%`);
  } else if (comparison.filledPercentage.trend === 'down') {
    insights.push(`时间利用率下降了${Math.abs(comparison.filledPercentage.value).toFixed(1)}%`);
  }

  if (insights.length === 0) {
    return `${currentDate}与${compareDate}的时间使用模式基本相似，保持了良好的一致性。`;
  }

  return insights.join('，') + '。';
}
