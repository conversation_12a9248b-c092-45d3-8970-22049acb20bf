'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppStore } from '@/stores/useAppStore';
import { ImportStrategy } from '@/utils/dataImport';
import ImportModal, { ImportStrategy as ModalImportStrategy } from './ImportModal';
import {
  Download,
  Upload,
  FileText,
  Database,
  ChevronDown,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

export function DataManagementDropdown() {
  const {
    dataManagement,
    setDataManagementDropdown,
    exportData,
    importData
  } = useAppStore();

  const [showImportModal, setShowImportModal] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setDataManagementDropdown(false);
      }
    }

    if (dataManagement.showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dataManagement.showDropdown, setDataManagementDropdown]);

  // 处理导出
  const handleExport = async (format: 'json' | 'csv') => {
    try {
      await exportData(format);
      setDataManagementDropdown(false);
    } catch (error) {
      console.error('导出失败:', error);
      // 这里可以添加错误提示
    }
  };

  // 处理导入数据按钮点击
  const handleImportClick = useCallback(() => {
    setShowImportModal(true);
    setDataManagementDropdown(false);
  }, [setDataManagementDropdown]);

  // 处理导入
  const handleImport = useCallback(async (file: File, strategy: ModalImportStrategy) => {
    // 转换策略类型
    const importStrategyMap: Record<ModalImportStrategy, ImportStrategy> = {
      'merge': ImportStrategy.MERGE,
      'overwrite': ImportStrategy.OVERWRITE,
      'append': ImportStrategy.ADD_ONLY
    };

    try {
      await importData(file, importStrategyMap[strategy]);
      // 不再设置showResult，让ImportModal自己处理成功状态
    } catch (error) {
      console.error('导入失败:', error);
      throw error; // 重新抛出错误，让ImportModal处理
    }
  }, [importData]);

  // 处理导入模态框关闭
  const handleImportModalClose = useCallback(() => {
    setShowImportModal(false);
  }, []);

  // 下拉菜单项组件
  const DropdownItem = ({ 
    icon: Icon, 
    label, 
    onClick, 
    disabled = false 
  }: {
    icon: React.ComponentType<any>;
    label: string;
    onClick: () => void;
    disabled?: boolean;
  }) => (
    <motion.button
      onClick={onClick}
      disabled={disabled}
      className={`w-full flex items-center px-4 py-3 text-left hover:bg-neutral-50 transition-colors ${
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
      }`}
      whileHover={disabled ? {} : { backgroundColor: 'var(--neutral-50)' }}
      whileTap={disabled ? {} : { scale: 0.98 }}
    >
      <Icon size={16} className="mr-3" style={{ color: 'var(--neutral-600)' }} />
      <span style={{ 
        fontSize: 'var(--font-size-sm)', 
        color: 'var(--neutral-700)',
        fontWeight: 'var(--font-weight-medium)'
      }}>
        {label}
      </span>
    </motion.button>
  );

  return (
    <div className="relative" ref={dropdownRef}>
      {/* 主按钮 */}
      <motion.button
        onClick={() => setDataManagementDropdown(!dataManagement.showDropdown)}
        className={`btn ${dataManagement.showDropdown ? 'btn-primary' : 'btn-secondary'} flex items-center`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.15 }}
        disabled={dataManagement.isExporting || dataManagement.isImporting}
      >
        {dataManagement.isExporting || dataManagement.isImporting ? (
          <Loader2 size={16} className="mr-2 animate-spin" />
        ) : (
          <Database size={16} className="mr-2" />
        )}
        数据管理
        <ChevronDown 
          size={14} 
          className={`ml-2 transition-transform ${
            dataManagement.showDropdown ? 'rotate-180' : ''
          }`} 
        />
      </motion.button>

      {/* 下拉菜单 */}
      <AnimatePresence>
        {dataManagement.showDropdown && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15, ease: [0.4, 0, 0.2, 1] }}
            className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-50"
            style={{
              borderColor: 'var(--neutral-200)',
              boxShadow: 'var(--shadow-lg)'
            }}
          >
            <div className="py-2">
              <DropdownItem
                icon={Download}
                label="导出 JSON"
                onClick={() => handleExport('json')}
                disabled={dataManagement.isExporting || dataManagement.isImporting}
              />
              <DropdownItem
                icon={FileText}
                label="导出 CSV"
                onClick={() => handleExport('csv')}
                disabled={dataManagement.isExporting || dataManagement.isImporting}
              />
              <div className="border-t my-2" style={{ borderColor: 'var(--neutral-200)' }} />
              <DropdownItem
                icon={Upload}
                label="导入数据"
                onClick={handleImportClick}
                disabled={dataManagement.isExporting || dataManagement.isImporting}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>



      {/* 新的Portal导入模态框 */}
      <ImportModal
        isOpen={showImportModal}
        onClose={handleImportModalClose}
        onImport={handleImport}
      />


    </div>
  );
}
