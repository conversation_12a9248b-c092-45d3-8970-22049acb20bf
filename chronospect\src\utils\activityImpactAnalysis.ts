import { TimeBlock, Activity } from '@/types';
import { formatDate, formatTime } from './timeUtils';

/**
 * 活动影响分析结果接口
 */
export interface ActivityImpactAnalysis {
  activityId: string;
  activityName: string;
  totalAffectedBlocks: number;
  affectedDatesCount: number;
  affectedDates: ActivityDateImpact[];
  summary: string;
}

/**
 * 单个日期的影响详情
 */
export interface ActivityDateImpact {
  date: string;
  formattedDate: string;
  affectedBlocks: number;
  timeRanges: ActivityTimeRange[];
}

/**
 * 时间范围信息
 */
export interface ActivityTimeRange {
  startTime: string;
  endTime: string;
  duration: number; // 分钟数
  blockCount: number;
}

/**
 * 分析删除活动的影响
 * @param activityId 要删除的活动ID
 * @param activity 活动对象
 * @param timeBlocks 所有时间块数据 (Record<string, TimeBlock[]>)
 * @returns 影响分析结果
 */
export function analyzeActivityDeletionImpact(
  activityId: string,
  activity: Activity,
  timeBlocks: Record<string, TimeBlock[]>
): ActivityImpactAnalysis {
  const affectedDates: ActivityDateImpact[] = [];
  let totalAffectedBlocks = 0;

  // 遍历所有日期的时间块
  Object.entries(timeBlocks).forEach(([date, blocks]) => {
    const affectedBlocksForDate = blocks.filter(block => block.activityId === activityId);
    
    if (affectedBlocksForDate.length > 0) {
      totalAffectedBlocks += affectedBlocksForDate.length;
      
      // 生成时间范围
      const timeRanges = generateTimeRangesFromBlocks(affectedBlocksForDate);
      
      affectedDates.push({
        date,
        formattedDate: formatDate(date),
        affectedBlocks: affectedBlocksForDate.length,
        timeRanges
      });
    }
  });

  // 按日期排序
  affectedDates.sort((a, b) => a.date.localeCompare(b.date));

  // 生成摘要信息
  const summary = generateImpactSummary(activity.name, totalAffectedBlocks, affectedDates.length);

  return {
    activityId,
    activityName: activity.name,
    totalAffectedBlocks,
    affectedDatesCount: affectedDates.length,
    affectedDates,
    summary
  };
}

/**
 * 将时间块转换为连续的时间范围
 * @param blocks 时间块数组
 * @returns 时间范围数组
 */
function generateTimeRangesFromBlocks(blocks: TimeBlock[]): ActivityTimeRange[] {
  if (blocks.length === 0) return [];

  // 按时间槽排序
  const sortedBlocks = [...blocks].sort((a, b) => a.timeSlot - b.timeSlot);
  const ranges: ActivityTimeRange[] = [];
  
  let currentRange: {
    startSlot: number;
    endSlot: number;
    blockCount: number;
  } | null = null;

  sortedBlocks.forEach(block => {
    if (currentRange === null) {
      // 开始新的范围
      currentRange = {
        startSlot: block.timeSlot,
        endSlot: block.timeSlot,
        blockCount: 1
      };
    } else if (block.timeSlot === currentRange.endSlot + 1) {
      // 连续的时间块，扩展当前范围
      currentRange.endSlot = block.timeSlot;
      currentRange.blockCount++;
    } else {
      // 不连续，保存当前范围并开始新的范围
      ranges.push(createTimeRangeFromSlots(currentRange));
      currentRange = {
        startSlot: block.timeSlot,
        endSlot: block.timeSlot,
        blockCount: 1
      };
    }
  });

  // 保存最后一个范围
  if (currentRange !== null) {
    ranges.push(createTimeRangeFromSlots(currentRange));
  }

  return ranges;
}

/**
 * 从时间槽创建时间范围对象
 * @param range 时间槽范围
 * @returns 时间范围对象
 */
function createTimeRangeFromSlots(range: {
  startSlot: number;
  endSlot: number;
  blockCount: number;
}): ActivityTimeRange {
  const startTime = formatTime(range.startSlot);
  const endTime = formatTime(range.endSlot + 1); // +1 因为endTime是下一个时间槽的开始时间
  const duration = range.blockCount * 30; // 每个时间块30分钟

  return {
    startTime: startTime.start,
    endTime: endTime.start,
    duration,
    blockCount: range.blockCount
  };
}

/**
 * 生成影响摘要信息
 * @param activityName 活动名称
 * @param totalBlocks 总影响时间块数
 * @param affectedDatesCount 影响的日期数
 * @returns 摘要字符串
 */
function generateImpactSummary(
  activityName: string,
  totalBlocks: number,
  affectedDatesCount: number
): string {
  if (totalBlocks === 0) {
    return `活动"${activityName}"当前未被使用，删除不会影响任何时间记录。`;
  }

  const totalMinutes = totalBlocks * 30;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  
  let timeDescription = '';
  if (hours > 0 && minutes > 0) {
    timeDescription = `${hours}小时${minutes}分钟`;
  } else if (hours > 0) {
    timeDescription = `${hours}小时`;
  } else {
    timeDescription = `${minutes}分钟`;
  }

  return `活动"${activityName}"在 ${affectedDatesCount} 个日期中被使用了 ${totalBlocks} 个时间段（共${timeDescription}）。删除此活动将清空这些时间记录。`;
}

/**
 * 检查活动是否为预设活动
 * @param activityName 活动名称
 * @returns 是否为预设活动
 */
export function isPresetActivity(activityName: string): boolean {
  const presetActivityNames = [
    '深度工作', '会议', '学习', '运动', '休息', '娱乐', '通勤', '用餐'
  ];
  return presetActivityNames.includes(activityName);
}

/**
 * 格式化时间范围显示
 * @param timeRange 时间范围
 * @returns 格式化的时间范围字符串
 */
export function formatTimeRange(timeRange: ActivityTimeRange): string {
  const durationText = timeRange.duration >= 60 
    ? `${Math.floor(timeRange.duration / 60)}小时${timeRange.duration % 60 > 0 ? `${timeRange.duration % 60}分钟` : ''}`
    : `${timeRange.duration}分钟`;
  
  return `${timeRange.startTime} - ${timeRange.endTime} (${durationText})`;
}
