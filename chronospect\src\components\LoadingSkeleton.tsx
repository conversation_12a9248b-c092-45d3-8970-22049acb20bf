'use client';

import React from 'react';
import { motion } from 'framer-motion';

/**
 * 基础骨架屏组件
 */
interface SkeletonProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  rounded?: boolean;
}

export function Skeleton({ 
  width = '100%', 
  height = '1rem', 
  className = '', 
  rounded = false 
}: SkeletonProps) {
  return (
    <motion.div
      className={`bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 ${
        rounded ? 'rounded-full' : 'rounded'
      } ${className}`}
      style={{ width, height }}
      animate={{
        backgroundPosition: ['0% 50%', '100% 50%', '0% 50%']
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: 'linear'
      }}
    />
  );
}

/**
 * 统计卡片骨架屏
 */
export function DailyStatsCardSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
      {/* 标题区域 */}
      <div className="mb-6">
        <Skeleton width="120px" height="24px" className="mb-2" />
        <Skeleton width="200px" height="16px" />
      </div>

      {/* 统计数据网格 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="text-center">
            <Skeleton width="40px" height="40px" rounded className="mx-auto mb-2" />
            <Skeleton width="60px" height="14px" className="mx-auto mb-1" />
            <Skeleton width="80px" height="18px" className="mx-auto" />
          </div>
        ))}
      </div>

      {/* 进度条区域 */}
      <div>
        <div className="flex justify-between items-center mb-2">
          <Skeleton width="100px" height="14px" />
          <Skeleton width="80px" height="14px" />
        </div>
        <Skeleton width="100%" height="12px" rounded />
      </div>
    </div>
  );
}

/**
 * 图表骨架屏
 */
export function ActivityChartSkeleton() {
  return (
    <div className="space-y-6">
      {/* 饼图骨架 */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <Skeleton width="120px" height="20px" className="mb-4" />
        <div className="h-80 flex items-center justify-center">
          <Skeleton width="200px" height="200px" rounded />
        </div>
      </div>

      {/* 条形图骨架 */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <Skeleton width="140px" height="20px" className="mb-4" />
        <div className="h-80 space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-4">
              <Skeleton width="80px" height="16px" />
              <Skeleton 
                width={`${Math.random() * 60 + 20}%`} 
                height="24px" 
                rounded 
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * 活动列表骨架屏
 */
export function ActivityListSkeleton() {
  return (
    <div className="space-y-6">
      {/* 活动排行榜骨架 */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <Skeleton width="100px" height="20px" className="mb-4" />
        <div className="space-y-3">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <Skeleton width="24px" height="24px" rounded />
                <div>
                  <Skeleton width="80px" height="16px" className="mb-1" />
                  <Skeleton width="60px" height="12px" />
                </div>
              </div>
              <Skeleton width="50px" height="16px" />
            </div>
          ))}
        </div>
      </div>

      {/* 时间段详情骨架 */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <Skeleton width="100px" height="20px" className="mb-4" />
        <div className="space-y-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3">
                <Skeleton width="16px" height="16px" rounded />
                <Skeleton width="120px" height="14px" />
              </div>
              <Skeleton width="80px" height="14px" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * 复盘视图完整骨架屏
 */
export function ReviewViewSkeleton() {
  return (
    <motion.div
      className="w-full max-w-6xl mx-auto px-4 py-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* 页面标题骨架 */}
      <div className="mb-8">
        <Skeleton width="120px" height="32px" className="mb-2" />
        <Skeleton width="200px" height="20px" />
      </div>

      {/* 统计概览卡片骨架 */}
      <div className="mb-8">
        <DailyStatsCardSkeleton />
      </div>

      {/* 主要内容区域骨架 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 左侧：数据可视化骨架 */}
        <div className="space-y-6">
          <ActivityChartSkeleton />
        </div>

        {/* 右侧：活动列表骨架 */}
        <div className="space-y-6">
          <ActivityListSkeleton />
        </div>
      </div>
    </motion.div>
  );
}

/**
 * 加载状态指示器
 */
interface LoadingIndicatorProps {
  message?: string;
  progress?: number;
}

export function LoadingIndicator({ message = '加载中...', progress }: LoadingIndicatorProps) {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <motion.div
        className="w-8 h-8 border-3 border-blue-200 border-t-blue-500 rounded-full"
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      />
      <p className="mt-4 text-gray-600 text-sm">{message}</p>
      {progress !== undefined && (
        <div className="w-48 bg-gray-200 rounded-full h-2 mt-2">
          <motion.div
            className="bg-blue-500 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      )}
    </div>
  );
}

/**
 * 数据加载错误组件
 */
interface LoadingErrorProps {
  message?: string;
  onRetry?: () => void;
}

export function LoadingError({
  message = '数据加载失败',
  onRetry
}: LoadingErrorProps) {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <div className="text-4xl mb-4">😵</div>
      <p className="text-gray-600 text-sm mb-4">{message}</p>
      {onRetry && (
        <motion.button
          onClick={onRetry}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          重试
        </motion.button>
      )}
    </div>
  );
}
